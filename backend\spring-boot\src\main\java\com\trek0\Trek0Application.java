package com.trek0;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * TREK0 应用主启动类
 * 
 * 扫描所有模块的组件、实体和Repository
 */
@SpringBootApplication(scanBasePackages = {
    "com.trek0.web",      // Web层
    "com.trek0.service",  // 服务层
    "com.trek0.infra",    // 基础设施层
    "com.trek0.config",   // 配置层
    "com.trek0.common"    // 通用组件
})
@EntityScan(basePackages = "com.trek0.domain.model")
@EnableJpaRepositories(basePackages = "com.trek0.infra.repository")
public class Trek0Application {
    
    public static void main(String[] args) {
        SpringApplication.run(Trek0Application.class, args);
    }
} 