# Scripts Module

## 职责说明
本模块包含数据库迁移、初始化、运维脚本等自动化脚本。

## 目录结构
```
scripts/
├── db/
│   ├── migration/          # Flyway数据库迁移脚本
│   ├── init/              # 数据库初始化脚本
│   └── backup/            # 数据库备份脚本
├── deploy/
│   ├── docker/            # Docker部署脚本
│   ├── kubernetes/        # K8s部署脚本
│   └── shell/             # Shell部署脚本
├── monitor/
│   ├── health-check/      # 健康检查脚本
│   └── metrics/           # 监控指标脚本
└── maintenance/
    ├── cleanup/           # 清理脚本
    └── backup/            # 备份脚本
```

## 使用方法
1. 数据库迁移：`mvn flyway:migrate`
2. 数据库初始化：`./scripts/db/init/init-db.sh`
3. 部署脚本：`./scripts/deploy/docker/deploy.sh`

## 注意事项
- 所有脚本需要适当的执行权限
- 生产环境执行前请先备份数据
- 脚本执行日志会记录到logs目录
