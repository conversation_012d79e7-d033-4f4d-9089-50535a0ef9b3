package com.trek0.web.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.sql.DataSource;
import java.sql.Connection;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <p>提供应用状态监控和健康检查功能。</p>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/health")
public class HealthController {
    
    private static final Logger log = LoggerFactory.getLogger(HealthController.class);

    private final DataSource dataSource;
    private final RedisTemplate<String, Object> redisTemplate;
    
    public HealthController(DataSource dataSource, RedisTemplate<String, Object> redisTemplate) {
        this.dataSource = dataSource;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 基础健康检查
     * 
     * @return 应用基础状态信息
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("version", "1.0.0");
        health.put("name", "Trek0 API");
        
        return ResponseEntity.ok(health);
    }

    /**
     * 详细健康检查
     * 
     * @return 包含各组件状态的详细健康信息
     */
    @GetMapping("/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        
        Map<String, Object> components = new HashMap<>();
        
        // 数据库健康检查
        Map<String, Object> database = checkDatabaseHealth();
        components.put("database", database);
        
        // Redis健康检查
        Map<String, Object> redis = checkRedisHealth();
        components.put("redis", redis);
        
        // 磁盘空间检查
        Map<String, Object> disk = checkDiskHealth();
        components.put("disk", disk);
        
        health.put("components", components);
        
        // 如果任何组件状态为DOWN，整体状态为DOWN
        boolean allUp = components.values().stream()
                .allMatch(component -> "UP".equals(((Map<String, Object>) component).get("status")));
        
        if (!allUp) {
            health.put("status", "DOWN");
        }
        
        return ResponseEntity.ok(health);
    }

    /**
     * 检查数据库连接状态
     * 
     * @return 数据库健康状态
     */
    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> database = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                database.put("status", "UP");
                database.put("message", "Database connection is healthy");
            } else {
                database.put("status", "DOWN");
                database.put("message", "Database connection is invalid");
            }
        } catch (Exception e) {
            log.error("Database health check failed", e);
            database.put("status", "DOWN");
            database.put("message", "Database connection failed: " + e.getMessage());
        }
        
        return database;
    }

    /**
     * 检查Redis连接状态
     * 
     * @return Redis健康状态
     */
    private Map<String, Object> checkRedisHealth() {
        Map<String, Object> redis = new HashMap<>();
        
        try {
            String ping = (String) redisTemplate.getConnectionFactory()
                    .getConnection()
                    .ping();
            
            if ("PONG".equals(ping)) {
                redis.put("status", "UP");
                redis.put("message", "Redis connection is healthy");
            } else {
                redis.put("status", "DOWN");
                redis.put("message", "Redis ping failed");
            }
        } catch (Exception e) {
            log.error("Redis health check failed", e);
            redis.put("status", "DOWN");
            redis.put("message", "Redis connection failed: " + e.getMessage());
        }
        
        return redis;
    }

    /**
     * 检查磁盘空间状态
     * 
     * @return 磁盘健康状态
     */
    private Map<String, Object> checkDiskHealth() {
        Map<String, Object> disk = new HashMap<>();
        
        try {
            java.io.File file = new java.io.File(".");
            long totalSpace = file.getTotalSpace();
            long freeSpace = file.getFreeSpace();
            long usedSpace = totalSpace - freeSpace;
            double usagePercent = (double) usedSpace / totalSpace * 100;
            
            disk.put("status", usagePercent < 90 ? "UP" : "DOWN");
            disk.put("totalSpace", totalSpace);
            disk.put("freeSpace", freeSpace);
            disk.put("usedSpace", usedSpace);
            disk.put("usagePercent", Math.round(usagePercent * 100.0) / 100.0);
            disk.put("message", usagePercent < 90 ? "Disk space is sufficient" : "Disk space is running low");
            
        } catch (Exception e) {
            log.error("Disk health check failed", e);
            disk.put("status", "DOWN");
            disk.put("message", "Disk check failed: " + e.getMessage());
        }
        
        return disk;
    }
}