package com.trek0.common.util;

import org.springframework.stereotype.Component;
import java.util.concurrent.atomic.AtomicLong;

/**
 * T0风格UUID生成器
 * 格式: T0 + 14位Base36编码
 * 示例: T0A7B2C9D8E5F1G3H6
 * 
 * 特性:
 * - 全局唯一性：基于时间戳+机器ID+序列号
 * - 高性能：单机每秒可生成409万个ID
 * - 可扩展：支持1024个节点
 * - 查询友好：按时间有序，便于数据库索引
 * - 企业级：满足100万用户并发需求
 */
@Component
public class T0IdGenerator {
    
    // T0前缀
    private static final String PREFIX = "T0";
    
    // 时间戳起始点 (2025-01-01 00:00:00 UTC)
    private static final long EPOCH = 1735689600000L;
    
    // 各部分位数
    private static final long SEQUENCE_BITS = 12L;
    private static final long MACHINE_ID_BITS = 10L;
    private static final long TIMESTAMP_BITS = 41L;
    
    // 最大值
    private static final long MAX_MACHINE_ID = (1L << MACHINE_ID_BITS) - 1;
    private static final long MAX_SEQUENCE = (1L << SEQUENCE_BITS) - 1;
    
    // 位移
    private static final long MACHINE_ID_SHIFT = SEQUENCE_BITS;
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;
    
    // 机器ID (0-1023)
    private final long machineId;
    
    // 序列号
    private final AtomicLong sequence = new AtomicLong(0L);
    
    // 上次时间戳
    private volatile long lastTimestamp = -1L;
    
    // Base36字符集
    private static final String BASE36_CHARS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    
    public T0IdGenerator() {
        // 默认机器ID为0，生产环境应该从配置中获取
        this(0L);
    }
    
    public T0IdGenerator(long machineId) {
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw new IllegalArgumentException(
                String.format("Machine ID must be between 0 and %d", MAX_MACHINE_ID));
        }
        this.machineId = machineId;
    }
    
    /**
     * 生成T0风格的UUID
     * @return T0 + 14位Base36编码的ID
     */
    public synchronized String generateId() {
        long timestamp = getCurrentTimestamp();
        
        // 时钟回拨检测
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", 
                    lastTimestamp - timestamp));
        }
        
        // 同一毫秒内序列号递增
        if (timestamp == lastTimestamp) {
            long seq = sequence.incrementAndGet() & MAX_SEQUENCE;
            if (seq == 0) {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp);
                sequence.set(0L);
            }
        } else {
            // 新的毫秒，序列号重置
            sequence.set(0L);
        }
        
        lastTimestamp = timestamp;
        
        // 组装64位ID
        long id = ((timestamp - EPOCH) << TIMESTAMP_SHIFT) |
                  (machineId << MACHINE_ID_SHIFT) |
                  sequence.get();
        
        // 转换为Base36编码
        String base36Id = encodeBase36(id, 14);
        
        return PREFIX + base36Id;
    }
    
    /**
     * 获取当前时间戳
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
    
    /**
     * 等待下一毫秒
     */
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }
    
    /**
     * 将数字编码为指定长度的Base36字符串
     */
    private String encodeBase36(long number, int width) {
        if (number == 0) {
            return "0".repeat(width);
        }
        
        StringBuilder sb = new StringBuilder();
        long num = number;
        
        while (num > 0) {
            sb.append(BASE36_CHARS.charAt((int)(num % 36)));
            num /= 36;
        }
        
        // 补齐长度
        while (sb.length() < width) {
            sb.append('0');
        }
        
        return sb.reverse().toString();
    }
    
    /**
     * 解析T0 ID获取时间戳（用于调试和分析）
     */
    public long parseTimestamp(String t0Id) {
        if (!t0Id.startsWith(PREFIX) || t0Id.length() != 16) {
            throw new IllegalArgumentException("Invalid T0 ID format");
        }
        
        String base36Part = t0Id.substring(2);
        long id = decodeBase36(base36Part);
        
        return ((id >> TIMESTAMP_SHIFT) & ((1L << TIMESTAMP_BITS) - 1)) + EPOCH;
    }
    
    /**
     * Base36解码
     */
    private long decodeBase36(String base36) {
        long result = 0;
        for (char c : base36.toCharArray()) {
            result = result * 36 + BASE36_CHARS.indexOf(c);
        }
        return result;
    }
    
    /**
     * 获取机器ID（用于调试）
     */
    public long getMachineId() {
        return machineId;
    }
}
