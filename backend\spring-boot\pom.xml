﻿<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trek0</groupId>
        <artifactId>trek0-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>trek0-spring-boot</artifactId>
    <name>trek0-spring-boot</name>
    <description>TREK0 Spring Boot 启动模块</description>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.trek0</groupId>
            <artifactId>trek0-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trek0</groupId>
            <artifactId>trek0-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.trek0</groupId>
            <artifactId>trek0-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.trek0.Trek0Application</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 