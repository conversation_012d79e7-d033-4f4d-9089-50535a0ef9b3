-- 企业标准用户表 - 重构版本
-- 符合企业级命名规范和数据结构标准

CREATE TABLE users (
    user_id BIGSERIAL PRIMARY KEY,
    user_uuid VARCHAR(36) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_signature VARCHAR(40),
    profile_image_url VARCHAR(500),
    last_ip_location VARCHAR(255),
    account_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP,
    
    -- 企业级约束
    CONSTRAINT chk_username_length CHECK (LENGTH(username) >= 3),
    CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_account_status CHECK (account_status IN ('ACTIVE', 'SUSPENDED', 'DELETED'))
);

-- 企业级索引策略
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_uuid ON users(user_uuid);
CREATE INDEX idx_users_created_at ON users(created_at DESC);
CREATE INDEX idx_users_last_login ON users(last_login_at DESC);
CREATE INDEX idx_users_account_status ON users(account_status);

-- 企业级注释
COMMENT ON TABLE users IS '企业标准用户表 - 重构版本';
COMMENT ON COLUMN users.user_id IS '用户主键ID';
COMMENT ON COLUMN users.user_uuid IS '用户业务唯一标识符';
COMMENT ON COLUMN users.username IS '用户名（3-50字符）';
COMMENT ON COLUMN users.email IS '邮箱地址（已验证格式）';
COMMENT ON COLUMN users.password_hash IS '密码哈希值（加密存储）';
COMMENT ON COLUMN users.user_signature IS '用户个人签名';
COMMENT ON COLUMN users.profile_image_url IS '用户头像图片URL';
COMMENT ON COLUMN users.last_ip_location IS '最后登录IP地理位置';
COMMENT ON COLUMN users.account_status IS '账户状态：ACTIVE-活跃, SUSPENDED-暂停, DELETED-已删除';
COMMENT ON COLUMN users.email_verified IS '邮箱是否已验证';
COMMENT ON COLUMN users.created_at IS '账户创建时间';
COMMENT ON COLUMN users.updated_at IS '账户最后更新时间';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';