-- 企业标准数据库重构完成脚本
-- 创建企业级视图、函数和触发器

-- 1. 创建用户统计视图
CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.user_id,
    u.username,
    u.email,
    u.created_at,
    u.last_login_at,
    COUNT(DISTINCT f.footprint_id) AS total_footprints,
    COUNT(DISTINCT fp.path_id) AS total_paths,
    COUNT(DISTINCT fph.photo_id) AS total_photos,
    COUNT(DISTINCT uf1.following_user_id) AS following_count,
    COUNT(DISTINCT uf2.follower_user_id) AS followers_count,
    COALESCE(SUM(f.view_count), 0) AS total_views,
    COALESCE(SUM(f.like_count), 0) AS total_likes
FROM users u
LEFT JOIN footprints f ON u.user_id = f.user_id AND f.footprint_status = 'ACTIVE'
LEFT JOIN footprint_paths fp ON f.footprint_id = fp.footprint_id AND fp.path_status = 'ACTIVE'
LEFT JOIN footprint_photos fph ON f.footprint_id = fph.footprint_id AND fph.photo_status = 'ACTIVE'
LEFT JOIN user_follows uf1 ON u.user_id = uf1.follower_user_id AND uf1.follow_status = 'ACTIVE'
LEFT JOIN user_follows uf2 ON u.user_id = uf2.following_user_id AND uf2.follow_status = 'ACTIVE'
WHERE u.account_status = 'ACTIVE'
GROUP BY u.user_id, u.username, u.email, u.created_at, u.last_login_at;

-- 2. 创建足迹详情视图
CREATE OR REPLACE VIEW footprint_details AS
SELECT 
    f.footprint_id,
    f.business_id,
    f.user_id,
    u.username,
    f.footprint_name,
    f.footprint_description,
    f.latitude,
    f.longitude,
    f.address_text,
    f.visibility_level,
    f.footprint_status,
    f.view_count,
    f.like_count,
    f.created_at,
    f.updated_at,
    COUNT(DISTINCT fp.path_id) AS path_count,
    COUNT(DISTINCT fph.photo_id) AS photo_count,
    COUNT(DISTINCT ft.tag_id) AS tag_count,
    COALESCE(SUM(fp.total_distance_meters), 0) AS total_distance,
    COALESCE(SUM(fp.total_duration_seconds), 0) AS total_duration
FROM footprints f
JOIN users u ON f.user_id = u.user_id
LEFT JOIN footprint_paths fp ON f.footprint_id = fp.footprint_id AND fp.path_status = 'ACTIVE'
LEFT JOIN footprint_photos fph ON f.footprint_id = fph.footprint_id AND fph.photo_status = 'ACTIVE'
LEFT JOIN footprint_tags ft ON f.footprint_id = ft.footprint_id
WHERE f.footprint_status = 'ACTIVE' AND u.account_status = 'ACTIVE'
GROUP BY f.footprint_id, f.business_id, f.user_id, u.username, f.footprint_name, 
         f.footprint_description, f.latitude, f.longitude, f.address_text, 
         f.visibility_level, f.footprint_status, f.view_count, f.like_count, 
         f.created_at, f.updated_at;

-- 3. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 4. 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_footprints_updated_at 
    BEFORE UPDATE ON footprints 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_footprint_paths_updated_at 
    BEFORE UPDATE ON footprint_paths 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. 创建业务ID生成函数
CREATE OR REPLACE FUNCTION generate_business_id(prefix TEXT, entity_id BIGINT)
RETURNS VARCHAR(50) AS $$
BEGIN
    RETURN prefix || '_' || LPAD(entity_id::TEXT, 10, '0') || '_' || 
           EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT;
END;
$$ LANGUAGE plpgsql;

-- 6. 创建安全审计日志表
CREATE TABLE audit_logs (
    audit_id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    operation_type VARCHAR(10) NOT NULL,
    record_id BIGINT NOT NULL,
    user_id BIGINT,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_operation_type CHECK (operation_type IN ('INSERT', 'UPDATE', 'DELETE'))
);

CREATE INDEX idx_audit_logs_table_operation ON audit_logs(table_name, operation_type);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);

-- 7. 创建性能监控表
CREATE TABLE performance_metrics (
    metric_id BIGSERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15, 4) NOT NULL,
    metric_unit VARCHAR(20),
    metric_category VARCHAR(50),
    recorded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_metric_value CHECK (metric_value >= 0)
);

CREATE INDEX idx_performance_metrics_name ON performance_metrics(metric_name, recorded_at DESC);
CREATE INDEX idx_performance_metrics_category ON performance_metrics(metric_category);

-- 8. 插入初始化数据
INSERT INTO performance_metrics (metric_name, metric_value, metric_unit, metric_category)
VALUES 
    ('database_version', 1.0, 'version', 'system'),
    ('migration_completed', 1.0, 'boolean', 'system'),
    ('enterprise_standards_applied', 1.0, 'boolean', 'system');

-- 企业级注释
COMMENT ON VIEW user_statistics IS '用户统计信息视图 - 企业级报表';
COMMENT ON VIEW footprint_details IS '足迹详情视图 - 包含关联数据统计';
COMMENT ON FUNCTION update_updated_at_column() IS '自动更新updated_at字段的触发器函数';
COMMENT ON FUNCTION generate_business_id(TEXT, BIGINT) IS '生成业务唯一标识符的函数';
COMMENT ON TABLE audit_logs IS '企业级审计日志表 - 记录所有数据变更';
COMMENT ON TABLE performance_metrics IS '性能监控指标表 - 系统性能追踪';