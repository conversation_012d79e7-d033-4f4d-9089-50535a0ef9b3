package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 性能监控指标实体类
 * 对应数据库表: performance_metrics
 * 用于记录系统性能监控数据
 */
@Entity
@Table(name = "performance_metrics", indexes = {
    @Index(name = "idx_performance_metrics_name", columnList = "metric_name, recorded_at"),
    @Index(name = "idx_performance_metrics_category", columnList = "metric_category")
})
public class PerformanceMetric {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "metric_id")
    private Long metricId;

    @NotBlank(message = "指标名称不能为空")
    @Size(max = 100, message = "指标名称长度不能超过100个字符")
    @Column(name = "metric_name", nullable = false, length = 100)
    private String metricName;

    @NotNull(message = "指标值不能为空")
    @DecimalMin(value = "0.0", message = "指标值必须大于等于0")
    @Column(name = "metric_value", nullable = false, precision = 15, scale = 4)
    private BigDecimal metricValue;

    @Size(max = 20, message = "指标单位长度不能超过20个字符")
    @Column(name = "metric_unit", length = 20)
    private String metricUnit;

    @Size(max = 50, message = "指标分类长度不能超过50个字符")
    @Column(name = "metric_category", length = 50)
    private String metricCategory;

    @Column(name = "recorded_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recordedAt;

    // 构造函数
    public PerformanceMetric() {
    }

    public PerformanceMetric(String metricName, BigDecimal metricValue) {
        this.metricName = metricName;
        this.metricValue = metricValue;
    }

    public PerformanceMetric(String metricName, BigDecimal metricValue, String metricUnit) {
        this.metricName = metricName;
        this.metricValue = metricValue;
        this.metricUnit = metricUnit;
    }

    public PerformanceMetric(String metricName, BigDecimal metricValue, String metricUnit, String metricCategory) {
        this.metricName = metricName;
        this.metricValue = metricValue;
        this.metricUnit = metricUnit;
        this.metricCategory = metricCategory;
    }

    // JPA生命周期方法
    @PrePersist
    protected void onCreate() {
        if (recordedAt == null) {
            recordedAt = LocalDateTime.now();
        }
    }

    // Getters and Setters
    public Long getMetricId() {
        return metricId;
    }

    public void setMetricId(Long metricId) {
        this.metricId = metricId;
    }

    public String getMetricName() {
        return metricName;
    }

    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }

    public BigDecimal getMetricValue() {
        return metricValue;
    }

    public void setMetricValue(BigDecimal metricValue) {
        this.metricValue = metricValue;
    }

    public String getMetricUnit() {
        return metricUnit;
    }

    public void setMetricUnit(String metricUnit) {
        this.metricUnit = metricUnit;
    }

    public String getMetricCategory() {
        return metricCategory;
    }

    public void setMetricCategory(String metricCategory) {
        this.metricCategory = metricCategory;
    }

    public LocalDateTime getRecordedAt() {
        return recordedAt;
    }

    public void setRecordedAt(LocalDateTime recordedAt) {
        this.recordedAt = recordedAt;
    }

    // 向后兼容性方法
    @JsonProperty("id")
    public Long getId() {
        return metricId;
    }

    public void setId(Long id) {
        this.metricId = id;
    }

    @JsonProperty("name")
    public String getName() {
        return metricName;
    }

    public void setName(String name) {
        this.metricName = name;
    }

    @JsonProperty("value")
    public BigDecimal getValue() {
        return metricValue;
    }

    public void setValue(BigDecimal value) {
        this.metricValue = value;
    }

    @JsonProperty("unit")
    public String getUnit() {
        return metricUnit;
    }

    public void setUnit(String unit) {
        this.metricUnit = unit;
    }

    @JsonProperty("category")
    public String getCategory() {
        return metricCategory;
    }

    public void setCategory(String category) {
        this.metricCategory = category;
    }

    @JsonProperty("timestamp")
    public LocalDateTime getTimestamp() {
        return recordedAt;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.recordedAt = timestamp;
    }

    // 业务方法
    public boolean isSystemMetric() {
        return "system".equals(metricCategory);
    }

    public boolean isPerformanceMetric() {
        return "performance".equals(metricCategory);
    }

    public boolean isBusinessMetric() {
        return "business".equals(metricCategory);
    }

    public boolean hasUnit() {
        return metricUnit != null && !metricUnit.trim().isEmpty();
    }

    public boolean hasCategory() {
        return metricCategory != null && !metricCategory.trim().isEmpty();
    }

    public String getFormattedValue() {
        if (hasUnit()) {
            return metricValue.toString() + " " + metricUnit;
        }
        return metricValue.toString();
    }

    public boolean isZeroValue() {
        return BigDecimal.ZERO.compareTo(metricValue) == 0;
    }

    public boolean isPositiveValue() {
        return metricValue.compareTo(BigDecimal.ZERO) > 0;
    }

    public double getDoubleValue() {
        return metricValue.doubleValue();
    }

    public long getLongValue() {
        return metricValue.longValue();
    }

    public int getIntValue() {
        return metricValue.intValue();
    }

    // 便捷的静态工厂方法
    public static PerformanceMetric createSystemMetric(String name, BigDecimal value, String unit) {
        PerformanceMetric metric = new PerformanceMetric(name, value, unit);
        metric.setMetricCategory("system");
        return metric;
    }

    public static PerformanceMetric createPerformanceMetric(String name, BigDecimal value, String unit) {
        return new PerformanceMetric(name, value, unit, "performance");
    }

    public static PerformanceMetric createBusinessMetric(String name, BigDecimal value, String unit) {
        return new PerformanceMetric(name, value, unit, "business");
    }

    public static PerformanceMetric createCounter(String name, long count) {
        return new PerformanceMetric(name, BigDecimal.valueOf(count), "count", "business");
    }

    public static PerformanceMetric createTimer(String name, double milliseconds) {
        return new PerformanceMetric(name, BigDecimal.valueOf(milliseconds), "ms", "performance");
    }

    public static PerformanceMetric createGauge(String name, double value, String unit) {
        return new PerformanceMetric(name, BigDecimal.valueOf(value), unit, "performance");
    }

    @Override
    public String toString() {
        return "PerformanceMetric{" +
                "metricId=" + metricId +
                ", metricName='" + metricName + '\'' +
                ", metricValue=" + metricValue +
                ", metricUnit='" + metricUnit + '\'' +
                ", metricCategory='" + metricCategory + '\'' +
                ", recordedAt=" + recordedAt +
                '}';
    }
}

