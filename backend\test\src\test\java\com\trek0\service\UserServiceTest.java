package com.trek0.service;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import com.trek0.service.impl.UserServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class UserServiceTest {
    @Mock
    private UserRepository userRepository;
    @InjectMocks
    private UserServiceImpl userService;
    @BeforeEach
    void setUp() { MockitoAnnotations.openMocks(this); }
    @Test
    void findUserById_found() {
        User user = new User();
        user.setId(1L);
        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        Optional<User> result = userService.findUserById(1L);
        assertTrue(result.isPresent());
        assertEquals(1L, result.get().getId());
    }
    @Test
    void findUserById_notFound() {
        when(userRepository.findById(2L)).thenReturn(Optional.empty());
        Optional<User> result = userService.findUserById(2L);
        assertFalse(result.isPresent());
    }
} 