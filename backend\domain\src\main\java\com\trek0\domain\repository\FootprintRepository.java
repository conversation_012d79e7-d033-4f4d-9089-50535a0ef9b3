package com.trek0.domain.repository;

import com.trek0.domain.model.Footprint;
import com.trek0.domain.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface FootprintRepository extends JpaRepository<Footprint, Long> {
    
    /**
     * 根据用户查找足迹
     */
    Page<Footprint> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);
    
    /**
     * 根据用户和可见性查找足迹
     */
    Page<Footprint> findByUserAndIsPublicOrderByCreatedAtDesc(User user, Boolean isPublic, Pageable pageable);
    
    /**
     * 查找公开的足迹
     */
    Page<Footprint> findByIsPublicTrueOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 根据时间范围查找足迹
     */
    Page<Footprint> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据用户和时间范围查找足迹
     */
    Page<Footprint> findByUserAndCreatedAtBetweenOrderByCreatedAtDesc(User user, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计用户的足迹数量
     */
    long countByUser(User user);
    
    /**
     * 统计用户的公开足迹数量
     */
    long countByUserAndIsPublicTrue(User user);
    
    /**
     * 查找最近的足迹
     */
    List<Footprint> findTop10ByOrderByCreatedAtDesc();
    
    /**
     * 根据地理位置范围查找足迹
     */
    @Query("SELECT f FROM Footprint f WHERE f.latitude BETWEEN :minLat AND :maxLat AND f.longitude BETWEEN :minLng AND :maxLng AND f.isPublic = true ORDER BY f.createdAt DESC")
    List<Footprint> findByLocationRange(@Param("minLat") Double minLatitude, 
                                       @Param("maxLat") Double maxLatitude,
                                       @Param("minLng") Double minLongitude, 
                                       @Param("maxLng") Double maxLongitude);

    java.util.Optional<Footprint> findByFootprintId(Long footprintId);
}

