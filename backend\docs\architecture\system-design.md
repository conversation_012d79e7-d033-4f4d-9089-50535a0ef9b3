# 系统设计文档

## 架构概览
- 分层：api、domain、service、infra、web、common、config、scripts、docs、test
- 技术栈：Spring Boot 3、JDK 21、PostgreSQL、Redis、Docker、K8s

## 主要模块
- api：DTO、接口、OpenAPI
- domain：领域模型、仓储接口
- service：业务逻辑、服务接口
- infra：数据实现、缓存、外部集成
- web：Controller、认证、异常
- common：工具、常量、枚举
- config：配置、AOP、限流、熔断
- scripts：迁移、部署、监控
- docs：文档、架构图
- test：测试

## 关键流程
- 用户注册/登录
- 足迹管理
- 安全认证与限流

## 架构图
![system-architecture](../diagrams/system-architecture.png) 