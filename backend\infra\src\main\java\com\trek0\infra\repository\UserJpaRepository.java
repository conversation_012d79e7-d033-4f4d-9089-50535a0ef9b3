package com.trek0.infra.repository;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户JPA仓储实现
 */
@Repository
public interface UserJpaRepository extends JpaRepository<User, Long>, UserRepository {
    
    Optional<User> findByUsername(String username);
    
    Optional<User> findByEmail(String email);
    
    Optional<User> findByUuid(String uuid);
    
    boolean existsByUsername(String username);
    
    boolean existsByEmail(String email);
    
    /**
     * 查询所有激活用户
     */
    @Query("SELECT u FROM User u WHERE u.accountStatus = 'ACTIVE'")
    List<User> findActiveUsers();
    
    /**
     * 查询最近登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt >= :since")
    List<User> findUsersLoggedInSince(@Param("since") LocalDateTime since);
    
    /**
     * 统计激活用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.accountStatus = 'ACTIVE'")
    long countActiveUsers();
}