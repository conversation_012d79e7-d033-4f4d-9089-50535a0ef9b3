package com.trek0.service;

import com.trek0.api.dto.CreatePathRequest;
import com.trek0.api.dto.FootprintPathDTO;
import com.trek0.domain.model.User;

import java.util.List;

/**
 * 足迹路径服务接口
 */
public interface FootprintPathService {
    
    /**
     * 为足迹添加路径
     */
    FootprintPathDTO addPathToFootprint(User user, Long footprintId, CreatePathRequest request);
    
    /**
     * 更新路径信息
     */
    FootprintPathDTO updatePath(User user, Long pathId, CreatePathRequest request);
    
    /**
     * 删除路径
     */
    void deletePath(User user, String pathId);
    
    /**
     * 获取路径详情（通过专属路径ID）
     */
    FootprintPathDTO getPath(String pathId, User currentUser);
    
    /**
     * 获取足迹的所有路径
     */
    List<FootprintPathDTO> getFootprintPaths(User currentUser, Long footprintId);
    
    /**
     * 搜索足迹的路径
     */
    List<FootprintPathDTO> searchFootprintPaths(User currentUser, Long footprintId, String keyword);
}