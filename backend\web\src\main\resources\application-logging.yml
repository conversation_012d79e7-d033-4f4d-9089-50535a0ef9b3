# 企业级日志配置
logging:
  level:
    root: INFO
    com.trek0.api: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.transaction: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    io.github.resilience4j: DEBUG
    io.micrometer: INFO
    
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{50}] [%X{traceId:-}] [%X{spanId:-}] - %msg%n"
    
  file:
    name: logs/trek0-api.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 3GB
    
  logback:
    rollingpolicy:
      file-name-pattern: logs/trek0-api-%d{yyyy-MM-dd}.%i.log.gz
      max-file-size: 100MB
      max-history: 30
      total-size-cap: 3GB
      clean-history-on-start: true

# 自定义日志配置
trek0:
  logging:
    # 性能监控日志
    performance:
      enabled: true
      slow-query-threshold: 500ms
      slow-request-threshold: 2000ms
      
    # 安全日志
    security:
      enabled: true
      log-failed-attempts: true
      log-successful-logins: true
      
    # 业务日志
    business:
      enabled: true
      log-user-actions: true
      log-footprint-operations: true
      
    # 错误日志
    error:
      enabled: true
      include-stack-trace: true
      notify-on-critical: true