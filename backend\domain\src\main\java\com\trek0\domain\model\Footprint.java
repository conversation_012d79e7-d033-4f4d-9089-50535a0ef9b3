package com.trek0.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "footprints")
public class Footprint {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "footprint_id")
    private Long footprintId; // 足迹唯一标识ID
    
    @Column(name = "business_id", nullable = false, unique = true, length = 50)
    private String businessId; // 业务唯一标识符
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @NotBlank(message = "足迹名称不能为空")
    @Size(max = 200, message = "足迹名称不能超过200个字符")
    @Column(name = "footprint_name", nullable = false, length = 200)
    private String footprintName;
    
    @Size(max = 1000, message = "描述不能超过1000个字符")
    @Column(name = "footprint_description", columnDefinition = "TEXT")
    private String footprintDescription;
    
    @NotNull(message = "纬度不能为空")
    @Column(nullable = false, precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @NotNull(message = "经度不能为空")
    @Column(nullable = false, precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @Size(max = 500, message = "地址不能超过500个字符")
    @Column(name = "address_text", length = 500)
    private String addressText;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "visibility_level", nullable = false, length = 20)
    private VisibilityLevel visibilityLevel = VisibilityLevel.PUBLIC;
    
    @Column(name = "last_action_method", length = 50)
    private String lastActionMethod;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "footprint_status", nullable = false, length = 20)
    private FootprintStatus footprintStatus = FootprintStatus.ACTIVE;
    
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;
    
    @Column(name = "like_count", nullable = false)
    private Integer likeCount = 0;
    
    @OneToMany(mappedBy = "footprint", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("createdAt ASC")
    private List<FootprintPath> paths = new ArrayList<>();
    
    @OneToMany(mappedBy = "footprint", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("createdAt ASC")
    private List<FootprintTag> tags = new ArrayList<>();
    
    @OneToMany(mappedBy = "footprint", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("displayOrder ASC, uploadedAt ASC")
    private List<FootprintPhoto> photos = new ArrayList<>();
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;
    
    public enum VisibilityLevel {
        PUBLIC, PRIVATE, FRIENDS_ONLY, UNLISTED
    }
    
    public enum FootprintStatus {
        ACTIVE, ARCHIVED, DELETED, DRAFT
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Constructors
    public Footprint() {}
    
    public Footprint(User user, String footprintName, BigDecimal latitude, BigDecimal longitude) {
        this.user = user;
        this.footprintName = footprintName;
        this.latitude = latitude;
        this.longitude = longitude;
    }
    
    // Getters and Setters
    public Long getFootprintId() {
        return footprintId;
    }
    
    public void setFootprintId(Long footprintId) {
        this.footprintId = footprintId;
    }
    
    // 保持向后兼容性
    public Long getId() {
        return footprintId;
    }
    
    public void setId(Long id) {
        this.footprintId = id;
    }
    
    public String getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public String getFootprintName() {
        return footprintName;
    }
    
    public void setFootprintName(String footprintName) {
        this.footprintName = footprintName;
    }
    
    // 保持向后兼容性
    public String getName() {
        return footprintName;
    }
    
    public void setName(String name) {
        this.footprintName = name;
    }
    
    public String getFootprintDescription() {
        return footprintDescription;
    }
    
    public void setFootprintDescription(String footprintDescription) {
        this.footprintDescription = footprintDescription;
    }
    
    // 保持向后兼容性
    public String getDescription() {
        return footprintDescription;
    }
    
    public void setDescription(String description) {
        this.footprintDescription = description;
    }
    
    public BigDecimal getLatitude() {
        return latitude;
    }
    
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    
    public BigDecimal getLongitude() {
        return longitude;
    }
    
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    
    public String getAddressText() {
        return addressText;
    }
    
    public void setAddressText(String addressText) {
        this.addressText = addressText;
    }
    
    // 保持向后兼容性
    public String getAddress() {
        return addressText;
    }
    
    public void setAddress(String address) {
        this.addressText = address;
    }
    
    public VisibilityLevel getVisibilityLevel() {
        return visibilityLevel;
    }
    
    public void setVisibilityLevel(VisibilityLevel visibilityLevel) {
        this.visibilityLevel = visibilityLevel;
    }
    
    // 保持向后兼容性
    public VisibilityLevel getVisibility() {
        return visibilityLevel;
    }
    
    public void setVisibility(VisibilityLevel visibility) {
        this.visibilityLevel = visibility;
    }
    
    // 兼容service层
    public boolean getIsPublic() { return visibilityLevel == VisibilityLevel.PUBLIC; }
    
    public String getLastActionMethod() {
        return lastActionMethod;
    }
    
    public void setLastActionMethod(String lastActionMethod) {
        this.lastActionMethod = lastActionMethod;
    }
    
    public FootprintStatus getFootprintStatus() {
        return footprintStatus;
    }
    
    public void setFootprintStatus(FootprintStatus footprintStatus) {
        this.footprintStatus = footprintStatus;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public List<FootprintPath> getPaths() {
        return paths;
    }
    
    public void setPaths(List<FootprintPath> paths) {
        this.paths = paths;
    }
    
    public List<FootprintTag> getTags() {
        return tags;
    }
    
    public void setTags(List<FootprintTag> tags) {
        this.tags = tags;
    }
    
    public List<FootprintPhoto> getPhotos() {
        return photos;
    }
    
    public void setPhotos(List<FootprintPhoto> photos) {
        this.photos = photos;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }
    
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }
    
    // Helper methods
    public void addPath(FootprintPath path) {
        paths.add(path);
        path.setFootprint(this);
    }
    
    public void removePath(FootprintPath path) {
        paths.remove(path);
        path.setFootprint(null);
    }
    
    // Tag management methods
    public void addTag(FootprintTag tag) {
        tags.add(tag);
        tag.setFootprint(this);
    }
    
    public void removeTag(FootprintTag tag) {
        tags.remove(tag);
        tag.setFootprint(null);
    }
    
    public void addTag(String tagName) {
        FootprintTag tag = new FootprintTag(this, tagName);
        addTag(tag);
    }
    
    public void addTag(String tagName, String tagCategory) {
        FootprintTag tag = new FootprintTag(this, tagName, tagCategory);
        addTag(tag);
    }
    
    public void addTag(String tagName, String tagCategory, String tagColor) {
        FootprintTag tag = new FootprintTag(this, tagName, tagCategory, tagColor);
        addTag(tag);
    }
    
    public boolean hasTag(String tagName) {
        return tags.stream().anyMatch(tag -> tag.getTagName().equals(tagName));
    }
    
    public void removeTagByName(String tagName) {
        tags.removeIf(tag -> tag.getTagName().equals(tagName));
    }
    
    // Photo management methods
    public void addPhoto(FootprintPhoto photo) {
        photos.add(photo);
        photo.setFootprint(this);
    }
    
    public void removePhoto(FootprintPhoto photo) {
        photos.remove(photo);
        photo.setFootprint(null);
    }
    
    public void addPhoto(String imageUrl) {
        FootprintPhoto photo = new FootprintPhoto(this, imageUrl);
        addPhoto(photo);
    }
    
    public void addPhoto(String imageUrl, String filename) {
        FootprintPhoto photo = new FootprintPhoto(this, imageUrl, filename);
        addPhoto(photo);
    }
    
    public FootprintPhoto getCoverPhoto() {
        return photos.stream()
                .filter(FootprintPhoto::getIsCoverPhoto)
                .findFirst()
                .orElse(null);
    }
    
    public void setCoverPhoto(FootprintPhoto photo) {
        // 移除其他封面照片
        photos.forEach(p -> p.setIsCoverPhoto(false));
        // 设置新的封面照片
        if (photo != null && photos.contains(photo)) {
            photo.setIsCoverPhoto(true);
        }
    }
    
    public List<FootprintPhoto> getActivePhotos() {
        return photos.stream()
                .filter(FootprintPhoto::isActive)
                .toList();
    }
    
    public int getPhotoCount() {
        return (int) photos.stream().filter(FootprintPhoto::isActive).count();
    }
    
    public int getTagCount() {
        return tags.size();
    }
    
    // 软删除方法
    public void softDelete() {
        this.deletedAt = LocalDateTime.now();
        this.footprintStatus = FootprintStatus.DELETED;
    }
    
    // 检查是否已删除
    public boolean isDeleted() {
        return deletedAt != null || footprintStatus == FootprintStatus.DELETED;
    }
    
    // 增加查看次数
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }
    
    // 增加点赞次数
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
    }
}

