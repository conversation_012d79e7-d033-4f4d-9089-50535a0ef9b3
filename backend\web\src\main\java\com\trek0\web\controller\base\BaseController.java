package com.trek0.web.controller.base;

import com.trek0.api.dto.UserDTO;
import com.trek0.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.HashMap;
import java.util.Map;

/**
 * 基础控制器类，提供通用方法
 */
public abstract class BaseController {
    
    protected final UserService userService;
    
    @Autowired
    public BaseController(UserService userService) {
        this.userService = userService;
    }
    
    /**
     * 获取当前登录用户
     */
    protected UserDTO getCurrentUser() {
        // 从JWT token或session中获取当前用户
        return null;
    }
    
    /**
     * 获取当前登录用户（可能为空）
     */
    protected UserDTO getCurrentUserOrNull() {
        // 从JWT token或session中获取当前用户，可能为null
        return null;
    }
    
    /**
     * 获取当前登录用户
     */
    protected UserDTO getCurrentUser(String userIdStr) {
        // 从JWT token或session中获取当前用户
        return null;
    }
    
    /**
     * 创建成功响应
     */
    protected Map<String, Object> createSuccessResponse(String message, Object data) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        if (data != null) {
            response.put("data", data);
        }
        return response;
    }
    
    /**
     * 创建错误响应
     */
    protected Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", message);
        return response;
    }
}