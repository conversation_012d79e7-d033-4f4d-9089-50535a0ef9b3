package com.trek0.service.impl;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import com.trek0.service.UserAuthService;
import com.trek0.service.VerificationService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class UserAuthServiceImpl implements UserAuthService {
    private static final Logger logger = LoggerFactory.getLogger(UserAuthServiceImpl.class);
    
    private final UserRepository userRepository;
    private final VerificationService verificationService;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Autowired
    public UserAuthServiceImpl(UserRepository userRepository, VerificationService verificationService) {
        this.userRepository = userRepository;
        this.verificationService = verificationService;
    }
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "findUserByEmailFallback")
    @Retry(name = "database")
    public Optional<User> findUserByEmail(String email) {
        try {
            logger.debug("Finding user by email: {}", email);
            Optional<User> user = userRepository.findByEmail(email);
            
            if (user.isPresent()) {
                // businessMetrics.incrementUserLogin(); // Removed BusinessMetrics
            }
            
            return user;
        } catch (Exception e) {
            // businessMetrics.incrementApiError("find_user_by_email_error"); // Removed BusinessMetrics
            logger.error("Error finding user by email: {}", email, e);
            throw e;
        }
    }
    
    // Fallback method for findUserByEmail
    public Optional<User> findUserByEmailFallback(String email, Exception ex) {
        logger.error("Fallback: Failed to find user by email: {}", email, ex);
        // businessMetrics.incrementApiError("find_user_by_email_fallback"); // Removed BusinessMetrics
        return Optional.empty();
    }
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "registerUserFallback")
    @Retry(name = "database")
    public User registerUser(String email, String password, String uuid) {
        try {
            logger.info("Registering new user with email: {}", email);
            
            User user = new User();
            user.setEmail(email);
            user.setUsername(uuid);
            user.setUuid(uuid);
            user.setPassword(passwordEncoder.encode(password));
            
            User savedUser = userRepository.save(user); // Removed BusinessMetrics
            
            logger.info("Successfully registered user with ID: {}", savedUser.getId());
            // businessMetrics.incrementUserRegistration(); // Removed BusinessMetrics
            
            return savedUser;
        } catch (Exception e) {
            // businessMetrics.incrementApiError("user_registration_error"); // Removed BusinessMetrics
            logger.error("Error registering user with email: {}", email, e);
            throw e;
        }
    }
    
    // Fallback method for registerUser
    public User registerUserFallback(String email, String password, String uuid, Exception ex) {
        logger.error("Fallback: Failed to register user with email: {}", email, ex);
        // businessMetrics.incrementApiError("user_registration_fallback"); // Removed BusinessMetrics
        throw new RuntimeException("用户注册服务暂时不可用，请稍后重试");
    }
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "resetPasswordFallback")
    @Retry(name = "database")
    public boolean resetPassword(String email, String code, String newPassword) {
        try {
            logger.info("Resetting password for user with email: {}", email);
            
            Optional<User> userOpt = userRepository.findByEmail(email); // Removed BusinessMetrics
            
            if (userOpt.isEmpty()) {
                logger.warn("User not found for password reset: {}", email);
                return false;
            }
            
            if (!verificationService.validateEmailCode(email, code)) {
                logger.warn("Invalid verification code for password reset: {}", email);
                // businessMetrics.incrementApiError("invalid_verification_code"); // Removed BusinessMetrics
                return false;
            }
            
            User user = userOpt.get();
            user.setPassword(passwordEncoder.encode(newPassword));
            
            userRepository.save(user); // Removed BusinessMetrics
            
            logger.info("Successfully reset password for user: {}", email);
            // businessMetrics.incrementPasswordReset(); // Removed BusinessMetrics
            
            return true;
        } catch (Exception e) {
            // businessMetrics.incrementApiError("password_reset_error"); // Removed BusinessMetrics
            logger.error("Error resetting password for user: {}", email, e);
            throw e;
        }
    }
    
    // Fallback method for resetPassword
    public boolean resetPasswordFallback(String email, String code, String newPassword, Exception ex) {
        logger.error("Fallback: Failed to reset password for user: {}", email, ex);
        // businessMetrics.incrementApiError("password_reset_fallback"); // Removed BusinessMetrics
        return false;
    }
}