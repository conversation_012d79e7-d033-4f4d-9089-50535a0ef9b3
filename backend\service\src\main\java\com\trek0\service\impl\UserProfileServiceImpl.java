package com.trek0.service.impl;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import com.trek0.service.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
public class UserProfileServiceImpl implements UserProfileService {
    private final UserRepository userRepository;
    @Autowired
    public UserProfileServiceImpl(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    @Override
    public Optional<User> findUserById(Long id) {
        return userRepository.findById(id);
    }
    @Override
    public Optional<User> findUserByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    @Override
    public User saveUser(User user) {
        return userRepository.save(user);
    }
}