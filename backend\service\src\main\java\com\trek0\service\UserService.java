package com.trek0.service;

import com.trek0.domain.model.User;

import java.util.List;
import java.util.Optional;

public interface UserService {
    List<User> findAllUsers();
    Optional<User> findUserById(Long id);
    Optional<User> findUserByEmail(String email);
    Optional<User> findUserByUsername(String username);
    User registerUser(String email, String username, String password);
    boolean validatePassword(User user, String password);
    User saveUser(User user);
    void deleteUser(Long id);
    void updateUserIpLocation(User user, String ipLocation);
    boolean changePassword(Long userId, String oldPassword, String newPassword);
}