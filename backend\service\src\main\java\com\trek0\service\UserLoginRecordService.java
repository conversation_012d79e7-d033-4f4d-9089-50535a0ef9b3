package com.trek0.service;

import com.trek0.domain.model.UserLoginRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户登录记录服务接口
 */
public interface UserLoginRecordService {
    
    /**
     * 异步记录用户登录信息
     */
    void recordLoginAsync(Long userId, String ipAddress, String userAgent);
    
    /**
     * 获取用户的登录记录（分页）
     */
    Page<UserLoginRecord> getUserLoginRecords(Long userId, Pageable pageable);
    
    /**
     * 获取用户最近的登录记录
     */
    List<UserLoginRecord> getRecentLoginRecords(Long userId);
    
    /**
     * 获取用户总登录次数
     */
    long getUserLoginCount(Long userId);
    
    /**
     * 根据用户ID获取最后一次登录记录
     */
    UserLoginRecord getLastLoginRecord(Long userId);
    
    /**
     * 根据用户ID和时间范围获取登录记录
     */
    List<UserLoginRecord> getUserLoginRecordsByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取用户登录热力图数据
     */
    Map<String, Object> getUserLoginHeatmapData(Long userId, int year, int month);
}