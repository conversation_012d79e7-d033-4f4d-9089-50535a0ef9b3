# 企业级数据库配置
# 仅在生产环境中使用
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    # 连接池配置
    hikari:
      # 连接池大小
      minimum-idle: 5
      maximum-pool-size: 20
      # 连接超时时间
      connection-timeout: 30000
      # 空闲连接超时时间
      idle-timeout: 600000
      # 连接最大生命周期
      max-lifetime: 1800000
      # 连接测试查询
      connection-test-query: SELECT 1
      # 连接池名称
      pool-name: Trek0HikariCP
      # 自动提交
      auto-commit: true
      # 连接初始化SQL
      connection-init-sql: SELECT 1
      # 验证超时时间
      validation-timeout: 5000
      # 泄漏检测阈值
      leak-detection-threshold: 60000
      
  jpa:
    # Hibernate配置
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    
    # JPA属性
    properties:
      hibernate:
        # SQL方言
        dialect: org.hibernate.dialect.PostgreSQLDialect
        # 格式化SQL
        format_sql: true
        # 显示SQL
        show_sql: false
        # 使用SQL注释
        use_sql_comments: true
        # 批处理大小
        jdbc:
          batch_size: 20
          fetch_size: 50
        # 二级缓存
        cache:
          use_second_level_cache: true
          use_query_cache: true
          region:
            factory_class: org.hibernate.cache.jcache.JCacheRegionFactory
        # 统计信息
        generate_statistics: true
        # 连接处理模式
        connection:
          handling_mode: delayed_acquisition_and_release_after_transaction
        # 事务配置
        transaction:
          jta:
            platform: org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform
            
    # 数据库平台
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    
    # 开放EntityManager在视图中
    open-in-view: false
    
  # 事务配置
  transaction:
    # 事务管理器
    default-timeout: 30
    rollback-on-commit-failure: true