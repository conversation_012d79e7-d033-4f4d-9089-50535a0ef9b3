-- 企业标准足迹路径表 - 重构版本

CREATE TABLE footprint_paths (
    path_id BIGSERIAL PRIMARY KEY,
    business_id VARCHAR(50) UNIQUE NOT NULL,
    footprint_id BIGINT NOT NULL,
    path_name VARCHAR(200) NOT NULL,
    path_description TEXT,
    path_type VARCHAR(30) NOT NULL DEFAULT 'WALKING',
    total_distance_meters DECIMAL(10, 2),
    total_duration_seconds INTEGER,
    elevation_gain_meters DECIMAL(8, 2),
    elevation_loss_meters DECIMAL(8, 2),
    max_elevation_meters DECIMAL(8, 2),
    min_elevation_meters DECIMAL(8, 2),
    path_difficulty VARCHAR(20),
    path_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_path_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(footprint_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_path_type CHECK (path_type IN ('WALKING', 'RUNNING', 'CYCLING', 'DRIVING', 'HIKING', 'CLIMBING', 'OTHER')),
    CONSTRAINT chk_distance CHECK (total_distance_meters IS NULL OR total_distance_meters >= 0),
    CONSTRAINT chk_duration CHECK (total_duration_seconds IS NULL OR total_duration_seconds >= 0),
    CONSTRAINT chk_elevation_gain CHECK (elevation_gain_meters IS NULL OR elevation_gain_meters >= 0),
    CONSTRAINT chk_elevation_loss CHECK (elevation_loss_meters IS NULL OR elevation_loss_meters >= 0),
    CONSTRAINT chk_path_difficulty CHECK (path_difficulty IN ('EASY', 'MODERATE', 'HARD', 'EXTREME', 'UNKNOWN')),
    CONSTRAINT chk_path_status CHECK (path_status IN ('ACTIVE', 'ARCHIVED', 'DELETED', 'DRAFT')),
    CONSTRAINT chk_path_name_length CHECK (LENGTH(path_name) >= 1)
);

-- 企业级索引
CREATE INDEX idx_footprint_paths_footprint_id ON footprint_paths(footprint_id);
CREATE INDEX idx_footprint_paths_business_id ON footprint_paths(business_id);
CREATE INDEX idx_footprint_paths_type ON footprint_paths(path_type);
CREATE INDEX idx_footprint_paths_distance ON footprint_paths(total_distance_meters DESC);
CREATE INDEX idx_footprint_paths_difficulty ON footprint_paths(path_difficulty);
CREATE INDEX idx_footprint_paths_created_at ON footprint_paths(created_at DESC);

-- 企业级注释
COMMENT ON TABLE footprint_paths IS '企业标准足迹路径表';
COMMENT ON COLUMN footprint_paths.path_id IS '路径主键ID';
COMMENT ON COLUMN footprint_paths.business_id IS '路径业务唯一标识符';
COMMENT ON COLUMN footprint_paths.footprint_id IS '足迹ID（外键）';
COMMENT ON COLUMN footprint_paths.path_name IS '路径名称';
COMMENT ON COLUMN footprint_paths.path_description IS '路径描述';
COMMENT ON COLUMN footprint_paths.path_type IS '路径类型';
COMMENT ON COLUMN footprint_paths.total_distance_meters IS '总距离（米）';
COMMENT ON COLUMN footprint_paths.total_duration_seconds IS '总耗时（秒）';
COMMENT ON COLUMN footprint_paths.elevation_gain_meters IS '累计爬升（米）';
COMMENT ON COLUMN footprint_paths.elevation_loss_meters IS '累计下降（米）';
COMMENT ON COLUMN footprint_paths.max_elevation_meters IS '最高海拔（米）';
COMMENT ON COLUMN footprint_paths.min_elevation_meters IS '最低海拔（米）';
COMMENT ON COLUMN footprint_paths.path_difficulty IS '路径难度等级';
COMMENT ON COLUMN footprint_paths.path_status IS '路径状态';
COMMENT ON COLUMN footprint_paths.created_at IS '创建时间';
COMMENT ON COLUMN footprint_paths.updated_at IS '更新时间';