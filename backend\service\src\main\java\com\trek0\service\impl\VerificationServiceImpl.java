package com.trek0.service.impl;

import com.trek0.service.VerificationService;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.PasswordAuthentication;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import java.util.concurrent.Executors;
import java.util.concurrent.ExecutorService;

@Service
public class VerificationServiceImpl implements VerificationService {
    private final Map<String, String> emailCodeMap = new ConcurrentHashMap<>();
    private final Map<String, Long> emailCodeTimeMap = new ConcurrentHashMap<>();
    private final ExecutorService executor = Executors.newCachedThreadPool();

    @Value("${mail.smtp.username}")
    private String username;

    @Value("${mail.smtp.password}")
    private String password;

    @Value("${mail.smtp.host}")
    private String host;

    @Value("${mail.smtp.port}")
    private String port;

    @Value("${mail.smtp.ssl.enable}")
    private String sslEnable;

    @Override
    public void sendEmailCode(String email, String type) {
        String code = String.format("%06d", (int)(Math.random() * 1000000));
        emailCodeMap.put(email, code);
        emailCodeTimeMap.put(email, System.currentTimeMillis());
        executor.submit(() -> {
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.port", port);
        props.put("mail.smtp.ssl.enable", sslEnable);
        Session session = Session.getInstance(props, new jakarta.mail.Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });
        try {
            Message message = new MimeMessage(session);
            message.setFrom(new InternetAddress(username));
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(email));
                if ("reset".equals(type)) {
                    message.setSubject("TREK0找回密码验证码");
                    message.setText("您的找回密码验证码是：" + code + "，5分钟内有效。");
                } else {
            message.setSubject("TREK0注册验证码");
            message.setText("您的验证码是：" + code + "，5分钟内有效。");
                }
            Transport.send(message);
        } catch (MessagingException e) {
                // 可加日志
            }
        });
        }

    @Override
    public void sendEmailCode(String email) {
        sendEmailCode(email, "register");
    }

    @Override
    public boolean validateEmailCode(String email, String code) {
        String real = emailCodeMap.get(email);
        Long time = emailCodeTimeMap.get(email);
        boolean valid = real != null && time != null && real.equals(code) && System.currentTimeMillis() - time <= 5 * 60 * 1000;
        if (valid) {
            emailCodeMap.remove(email);
            emailCodeTimeMap.remove(email);
        }
        return valid;
    }

    @Override
    public boolean validateCaptcha(String token) {
        // 本地开发环境直接放行，生产环境请务必恢复真实校验
        return true;
    }
}