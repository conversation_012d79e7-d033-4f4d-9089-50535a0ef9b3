package com.trek0.service.impl;

import com.trek0.domain.model.User;
import com.trek0.domain.model.Footprint;
import com.trek0.service.FootprintPermissionService;
import org.springframework.stereotype.Service;

/**
 * 足迹权限管理服务实现
 */
@Service
public class FootprintPermissionServiceImpl implements FootprintPermissionService {
    
    @Override
    public boolean hasAccessPermission(Footprint footprint, User user) {
        if (footprint == null || user == null) {
            return false;
        }
        
        // 足迹所有者总是有访问权限
        if (footprint.getUser().getId().equals(user.getId())) {
            return true;
        }
        
        // 公开足迹任何人都可以访问
        if (footprint.getIsPublic()) {
            return true;
        }
        
        // 私有足迹只有所有者可以访问
        return false;
    }
    
    @Override
    public boolean hasEditPermission(Footprint footprint, User user) {
        if (footprint == null || user == null) {
            return false;
        }
        
        // 只有足迹所有者可以编辑
        return footprint.getUser().getId().equals(user.getId());
    }
    
    @Override
    public boolean hasDeletePermission(Footprint footprint, User user) {
        if (footprint == null || user == null) {
            return false;
        }
        
        // 只有足迹所有者可以删除
        return footprint.getUser().getId().equals(user.getId());
    }
    
    @Override
    public boolean canViewPrivateInfo(Footprint footprint, User user) {
        if (footprint == null || user == null) {
            return false;
        }
        
        // 只有足迹所有者可以查看私有信息
        return footprint.getUser().getId().equals(user.getId());
    }
    
    @Override
    public void validateAccessPermission(Footprint footprint, User user) {
        if (!hasAccessPermission(footprint, user)) {
            throw new RuntimeException("无权访问此足迹");
        }
    }
    
    @Override
    public void validateEditPermission(Footprint footprint, User user) {
        if (!hasEditPermission(footprint, user)) {
            throw new RuntimeException("无权修改此足迹");
        }
    }
}