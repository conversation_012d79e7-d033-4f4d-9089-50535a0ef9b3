package org.lionsoul.ip2region;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.channels.FileChannel;

public class DbSearcher {
    private RandomAccessFile raf;
    private FileChannel fileChannel;
    private ByteBuffer buffer;
    private String dbFile;

    public DbSearcher(DbConfig config, String dbFile) throws IOException {
        this.dbFile = dbFile;
        this.raf = new RandomAccessFile(dbFile, "r");
        this.fileChannel = raf.getChannel();
        this.buffer = ByteBuffer.allocate(config.getIoBufferSize());
        this.buffer.order(ByteOrder.LITTLE_ENDIAN);
    }

    public DataBlock binarySearch(String ip) throws IOException {
        long ipLong = ip2long(ip);
        int low = 0, high = (int) ((raf.length() - 262144 - 4) / 12) - 1;
        int dataPtr = 0;
        while (low <= high) {
            int mid = (low + high) >>> 1;
            raf.seek(262144 + mid * 12);
            long sip = readLong(raf);
            long eip = readLong(raf);
            dataPtr = readInt(raf);
            if (ipLong < sip) {
                high = mid - 1;
            } else if (ipLong > eip) {
                low = mid + 1;
            } else {
                break;
            }
        }
        if (dataPtr == 0) return new DataBlock(0, "未知");
        raf.seek(dataPtr);
        int cityId = readInt(raf);
        byte[] buf = new byte[32];
        int len = raf.read(buf);
        String region = new String(buf, 0, len, "UTF-8").split("\0")[0];
        return new DataBlock(cityId, region);
    }

    private long ip2long(String ip) {
        String[] segs = ip.split("\\.");
        long res = 0;
        for (int i = 0; i < 4; i++) {
            res = (res << 8) | Integer.parseInt(segs[i]);
        }
        return res;
    }

    private long readLong(RandomAccessFile raf) throws IOException {
        return ((raf.readByte() & 0xFFL) << 24) | ((raf.readByte() & 0xFFL) << 16)
                | ((raf.readByte() & 0xFFL) << 8) | (raf.readByte() & 0xFFL);
    }

    private int readInt(RandomAccessFile raf) throws IOException {
        return ((raf.readByte() & 0xFF) << 24) | ((raf.readByte() & 0xFF) << 16)
                | ((raf.readByte() & 0xFF) << 8) | (raf.readByte() & 0xFF);
    }

    public void close() throws IOException {
        if (raf != null) raf.close();
        if (fileChannel != null) fileChannel.close();
    }

    public static class DataBlock {
        private int cityId;
        private String region;
        public DataBlock(int cityId, String region) {
            this.cityId = cityId;
            this.region = region;
        }
        public int getCityId() { return cityId; }
        public String getRegion() { return region; }
    }
}