package com.trek0.service;

import com.trek0.domain.model.User;

/**
 * 企业级审计服务接口
 * 用于记录所有安全相关操作和合规性日志
 */
public interface AuditService {
    
    /**
     * 记录用户登录事件
     */
    void logUserLogin(User user, String ipAddress, String userAgent, boolean success);
    
    /**
     * 记录用户登出事件
     */
    void logUserLogout(User user, String ipAddress);
    
    /**
     * 记录数据访问事件
     */
    void logDataAccess(User user, String resourceType, String resourceId, String action, String ipAddress);
    
    /**
     * 记录数据修改事件
     */
    void logDataModification(User user, String resourceType, String resourceId, String action, String details, String ipAddress);
    
    /**
     * 记录权限验证失败事件
     */
    void logPermissionDenied(User user, String resourceType, String resourceId, String action, String ipAddress);
    
    /**
     * 记录安全异常事件
     */
    void logSecurityException(String exceptionType, String message, String ipAddress, String userAgent);
    
    /**
     * 记录API调用事件
     */
    void logApiCall(User user, String endpoint, String method, String ipAddress, int responseCode, long duration);
    
    /**
     * 记录敏感操作事件
     */
    void logSensitiveOperation(User user, String operation, String details, String ipAddress);
}