# Web Module

## 职责说明
Web模块是系统的表现层，包含：
- REST API控制器
- 请求/响应处理
- 参数校验
- 全局异常处理
- 安全认证
- API文档配置

## 模块依赖
- 依赖：`service`、`api`、`common`、`config`模块
- 被依赖：无（最顶层模块）

## 包结构
```
com.trek0.web/
├── controller/        # REST控制器
│   ├── AuthController.java
│   ├── UserController.java
│   ├── FootprintController.java
│   └── base/          # 基础控制器
├── interceptor/       # 拦截器
├── exception/         # 异常处理
├── security/          # 安全配置
└── config/            # Web配置
```

## 核心控制器
- **AuthController**: 认证相关API
- **UserController**: 用户管理API
- **FootprintController**: 足迹管理API
- **ProfileController**: 用户资料API

## 开发规范
- 只负责HTTP交互，不包含业务逻辑
- 使用DTO进行数据传输
- 统一异常处理和响应格式
- 参数校验使用Bean Validation
- 安全认证使用Spring Security
- API文档使用OpenAPI/Swagger 