# 生产环境配置
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:trek0_user}
    password: ${DB_PASSWORD:trek0_password}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true

logging:
  level:
    root: WARN
    com.trek0.api: INFO
    org.springframework.web: WARN
    io.github.resilience4j: WARN

trek0:
  security:
    login:
      max-attempts: 3 # 生产环境更严格 