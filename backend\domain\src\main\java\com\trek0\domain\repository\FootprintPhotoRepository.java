package com.trek0.domain.repository;

import com.trek0.domain.model.FootprintPhoto;
import com.trek0.domain.model.Footprint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FootprintPhotoRepository extends JpaRepository<FootprintPhoto, Long> {
    
    /**
     * 根据足迹查找所有照片
     */
    List<FootprintPhoto> findByFootprintOrderByDisplayOrderAsc(Footprint footprint);
    
    /**
     * 根据足迹ID查找所有照片
     */
    List<FootprintPhoto> findByFootprintFootprintIdOrderByDisplayOrderAsc(Long footprintId);
    
    /**
     * 根据足迹查找活跃状态的照片
     */
    List<FootprintPhoto> findByFootprintAndPhotoStatusOrderByDisplayOrderAsc(Footprint footprint, FootprintPhoto.PhotoStatus status);
    
    /**
     * 查找足迹的封面照片
     */
    Optional<FootprintPhoto> findByFootprintAndIsCoverPhotoTrue(Footprint footprint);
    
    /**
     * 根据图片URL查找照片
     */
    Optional<FootprintPhoto> findByPhotoUrl(String photoUrl);
    
    /**
     * 根据文件名查找照片
     */
    List<FootprintPhoto> findByOriginalFilename(String originalFilename);
    
    /**
     * 统计足迹的照片数量
     */
    long countByFootprint(Footprint footprint);
    
    /**
     * 统计足迹的活跃照片数量
     */
    long countByFootprintAndPhotoStatus(Footprint footprint, FootprintPhoto.PhotoStatus status);
    
    /**
     * 根据上传方式查找照片
     */
    List<FootprintPhoto> findByUploadMethod(FootprintPhoto.UploadMethod uploadMethod);
    
    /**
     * 根据图片格式查找照片
     */
    List<FootprintPhoto> findByImageFormat(FootprintPhoto.ImageFormat imageFormat);
    
    /**
     * 查找大于指定大小的照片
     */
    @Query("SELECT p FROM FootprintPhoto p WHERE p.imageWidth * p.imageHeight > :size")
    List<FootprintPhoto> findPhotosLargerThan(@Param("size") Long size);
    
    /**
     * 删除足迹的所有照片
     */
    void deleteByFootprint(Footprint footprint);
    
    /**
     * 查找处理失败的照片
     */
    List<FootprintPhoto> findByPhotoStatus(FootprintPhoto.PhotoStatus status);
}