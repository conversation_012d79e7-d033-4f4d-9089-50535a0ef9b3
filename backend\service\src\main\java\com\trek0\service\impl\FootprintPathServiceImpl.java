package com.trek0.service.impl;

import com.trek0.api.dto.CreatePathRequest;
import com.trek0.domain.model.User;
import com.trek0.domain.model.Footprint;
import com.trek0.api.dto.FootprintPathDTO;
import com.trek0.domain.model.*;
import com.trek0.domain.repository.FootprintRepository;
import com.trek0.domain.repository.FootprintPathRepository;
import com.trek0.service.FootprintPathService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.math.BigDecimal;

@Service
@Transactional
public class FootprintPathServiceImpl implements FootprintPathService {
    
    private static final Logger logger = LoggerFactory.getLogger(FootprintPathServiceImpl.class);
    
    private final FootprintRepository footprintRepository;
    private final FootprintPathRepository pathRepository;
    
    @Autowired
    public FootprintPathServiceImpl(FootprintRepository footprintRepository,
                                    FootprintPathRepository pathRepository) {
        this.footprintRepository = footprintRepository;
        this.pathRepository = pathRepository;
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "addPathToFootprintFallback")
    @Retry(name = "database")
    public FootprintPathDTO addPathToFootprint(User user, Long footprintId, CreatePathRequest request) {
        logger.info("Adding path to footprint {} for user: {}", footprintId, user.getUsername());
        
        try {
            // 根据footprintId获取足迹信息
            Footprint footprint = footprintRepository.findByFootprintId(footprintId).orElse(null);
            
            // 如果找不到，抛出异常
            if (footprint == null) {
                throw new RuntimeException("足迹不存在");
            }
            
            // 检查权限
            if (!footprint.getUser().getId().equals(user.getId())) {
                throw new RuntimeException("无权修改此足迹");
            }
            
            FootprintPath path = new FootprintPath();
            path.setFootprint(footprint);
            
            // 生成专属路径ID（新的统一格式）
            // path.setBusinessId(pathIdGenerator.generatePathId()); // Removed
            
            path.setName(request.getName());
            path.setDescription(request.getDescription());
            
            // 设置路径类型
            try {
                path.setType(FootprintPath.PathType.valueOf(request.getType().toUpperCase()));
            } catch (IllegalArgumentException e) {
                path.setType(FootprintPath.PathType.WALKING);
            }
            
            path.setDistance(request.getDistance());
            path.setDuration(request.getDuration());
            
            // 添加路径点
            if (request.getPoints() != null) {
                for (int i = 0; i < request.getPoints().size(); i++) {
                    CreatePathRequest.PathPointRequest pointReq = request.getPoints().get(i);
                    PathPoint point = new PathPoint();
                    point.setLatitudeDouble(pointReq.getLat());
                    point.setLongitudeDouble(pointReq.getLng());
                    point.setElevation(pointReq.getElevation());
                    point.setTimestamp(pointReq.getTimestamp());
                    point.setSequence(i);
                    path.addPoint(point);
                }
            }
            
            footprint.addPath(path);
            
            Footprint savedFootprint = footprintRepository.save(footprint); // Removed businessMetrics.recordDatabaseQueryTime
            
            logger.info("Added path {} to footprint {}", path.getName(), footprint.getFootprintId());
            // businessMetrics.incrementPathCreation(); // Removed
            
            return FootprintPathDTO.fromEntity(path);
        } catch (Exception e) {
            // businessMetrics.incrementApiError("path_creation_error"); // Removed
            logger.error("Error adding path to footprint {} for user: {}", footprintId, user.getUsername(), e);
            throw e;
        }
    }
    
    // Fallback method for addPathToFootprint
    public FootprintPathDTO addPathToFootprintFallback(User user, Long footprintId, CreatePathRequest request, Exception ex) {
        logger.error("Fallback: Failed to add path to footprint {} for user: {}", footprintId, user.getUsername(), ex);
        // businessMetrics.incrementApiError("path_creation_fallback"); // Removed
        throw new RuntimeException("添加路径服务暂时不可用，请稍后重试");
    }
    
    @Override
    public FootprintPathDTO updatePath(User user, Long pathId, CreatePathRequest request) {
        logger.info("Updating path {} for user: {}", pathId, user.getUsername());
        FootprintPath path = pathRepository.findById(pathId)
            .orElseThrow(() -> new RuntimeException("路径不存在"));
        // 权限校验（如有）
        if (!path.getFootprint().getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权修改此路径");
        }
        // 只更新需要变更的字段
        path.setName(request.getName());
        path.setDescription(request.getDescription());
        try {
            path.setType(FootprintPath.PathType.valueOf(request.getType().toUpperCase()));
        } catch (Exception e) {
            // 类型不合法时不更新
        }
        path.setDistance(request.getDistance());
        path.setDuration(request.getDuration());
        // 更新路径点
        if (request.getPoints() != null) {
            path.clearPoints();
            for (int i = 0; i < request.getPoints().size(); i++) {
                CreatePathRequest.PathPointRequest pointReq = request.getPoints().get(i);
                PathPoint point = new PathPoint();
                point.setLatitudeDouble(pointReq.getLat());
                point.setLongitudeDouble(pointReq.getLng());
                point.setElevation(pointReq.getElevation());
                point.setTimestamp(pointReq.getTimestamp());
                point.setSequence(i);
                path.addPoint(point);
            }
        }
        // JPA 自动 update
        return FootprintPathDTO.fromEntity(pathRepository.save(path));
    }
    
    @Override
    public void deletePath(User user, String pathId) {
        FootprintPath path = pathRepository.findByPathId(Long.parseLong(pathId))
            .orElseThrow(() -> new RuntimeException("路径不存在"));
        
        // 检查权限
        if (!path.getFootprint().getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权删除此路径");
        }
        
        Footprint footprint = path.getFootprint();
        footprint.removePath(path);
        footprintRepository.save(footprint);
        
        logger.info("Deleted path {}", path.getName());
    }
    
    @Override
    public FootprintPathDTO getPath(String pathId, com.trek0.domain.model.User currentUser) {
        // 示例实现：根据pathId查找路径并转换为DTO
        Long id = Long.parseLong(pathId);
        return pathRepository.findByPathId(id)
            .map(FootprintPathDTO::fromEntity)
            .orElse(null);
    }
    
    @Override
    public List<FootprintPathDTO> getFootprintPaths(User currentUser, Long footprintId) {
        logger.info("Getting footprint paths for ID: {}", footprintId);
        
        // 根据footprintId获取足迹信息
        Footprint footprint = footprintRepository.findByFootprintId(footprintId).orElse(null);
        
        // 如果找不到，抛出异常
        if (footprint == null) {
            logger.error("Footprint not found for ID: {}", footprintId);
            throw new RuntimeException("足迹不存在");
        }
        
        logger.info("Found footprint: ID={}, FootprintId={}, Name={}", 
                    footprint.getId(), footprint.getFootprintId(), footprint.getName());
        
        // 检查访问权限
        if (footprint.getVisibility() == Footprint.VisibilityLevel.PRIVATE && 
            (currentUser == null || !footprint.getUser().getId().equals(currentUser.getId()))) {
            logger.warn("Access denied to private footprint: {}", footprint.getId());
            throw new RuntimeException("无权访问此足迹的路径");
        }
        
        // 获取路径列表
        logger.info("Fetching paths for footprint ID: {}", footprint.getId());
        
        try {
            List<FootprintPath> paths = pathRepository.findByFootprintIdOrderByCreatedAtAsc(footprint.getId());
            logger.info("Found {} paths for footprint", paths.size());
            
            List<FootprintPathDTO> result = paths.stream()
                .map(path -> {
                    logger.info("Converting path: ID={}, Name={}", path.getId(), path.getName());
                    return FootprintPathDTO.fromEntity(path);
                })
                .collect(Collectors.toList());
            
            logger.info("Successfully converted {} paths to DTOs", result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("Error fetching or converting paths", e);
            throw new RuntimeException("处理路径数据时出错: " + e.getMessage());
        }
    }
    
    @Override
    public List<FootprintPathDTO> searchFootprintPaths(User currentUser, Long footprintId, String keyword) {
        // 直接用 Long 类型 footprintId 查询
        List<FootprintPath> paths = pathRepository.searchByFootprintIdAndKeyword(footprintId, keyword);
        return paths.stream().map(FootprintPathDTO::fromEntity).collect(Collectors.toList());
    }
}