# Server Configuration
server.port=8080

# Database Configuration
spring.datasource.url=****************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=postgres
spring.datasource.password=080706
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update

# Logging Configuration
logging.level.com.trek0=DEBUG

# Application Properties
app.name=Trek0
app.version=1.0.0 

# Email SMTP Configuration
mail.smtp.username=<EMAIL>
mail.smtp.password=UW3ZJ34dpSNkwAmn
mail.smtp.host=smtp.163.com
mail.smtp.port=465
mail.smtp.ssl.enable=true

# reCAPTCHA
recaptcha.secret=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe
recaptcha.url=https://www.google.com/recaptcha/api/siteverify 

# hCaptcha
hcaptcha.sitekey=10000000-ffff-ffff-ffff-000000000001 

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# 高德地图key配置
amap.key=e07fc210343a94f7b601263a0846b6b0

# 百度地图key配置
baidu.key=wLID48JjNTDmhnhWY2fbmG6nt9BBW1pB 