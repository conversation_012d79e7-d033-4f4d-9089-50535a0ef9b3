package com.trek0.service;

import com.trek0.domain.model.User;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.extension.ExtendWith;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest {
    @Mock
    private UserService userService;

    @Test
    void testFindUserById() {
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        when(userService.findUserById(1L)).thenReturn(Optional.of(user));
        Optional<User> result = userService.findUserById(1L);
        assertTrue(result.isPresent());
        assertEquals("testuser", result.get().getUsername());
    }

    @Test
    void testRegisterUser() {
        User user = new User();
        user.setEmail("<EMAIL>");
        user.setUsername("testuser");
        user.setPassword("password");
        when(userService.registerUser(anyString(), anyString(), anyString())).thenReturn(user);
        User result = userService.registerUser("<EMAIL>", "password", "uuid-123");
        assertNotNull(result);
        assertEquals("testuser", result.getUsername());
    }
} 