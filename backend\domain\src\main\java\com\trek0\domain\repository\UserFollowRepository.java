package com.trek0.domain.repository;

import com.trek0.domain.model.User;
import com.trek0.domain.model.UserFollow;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserFollowRepository extends JpaRepository<UserFollow, Long> {
    
    /**
     * 查找用户的关注列表
     */
    Page<UserFollow> findByFollowerAndFollowStatusOrderByFollowedAtDesc(User follower, UserFollow.FollowStatus status, Pageable pageable);
    
    /**
     * 查找用户的粉丝列表
     */
    Page<UserFollow> findByFollowingAndFollowStatusOrderByFollowedAtDesc(User following, UserFollow.FollowStatus status, Pageable pageable);
    
    /**
     * 检查关注关系是否存在
     */
    Optional<UserFollow> findByFollowerAndFollowing(User follower, User following);
    
    /**
     * 检查是否为活跃关注关系
     */
    boolean existsByFollowerAndFollowingAndFollowStatus(User follower, User following, UserFollow.FollowStatus status);
    
    /**
     * 统计用户的关注数量
     */
    long countByFollowerAndFollowStatus(User follower, UserFollow.FollowStatus status);
    
    /**
     * 统计用户的粉丝数量
     */
    long countByFollowingAndFollowStatus(User following, UserFollow.FollowStatus status);
    
    /**
     * 查找互相关注的用户
     */
    @Query("SELECT uf1 FROM UserFollow uf1 WHERE uf1.follower = :user AND uf1.followStatus = :status AND " +
           "EXISTS (SELECT uf2 FROM UserFollow uf2 WHERE uf2.follower = uf1.following AND uf2.following = :user AND uf2.followStatus = :status)")
    List<UserFollow> findMutualFollows(@Param("user") User user, @Param("status") UserFollow.FollowStatus status);
}

