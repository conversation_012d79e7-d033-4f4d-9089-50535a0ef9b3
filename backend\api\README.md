# API Module

## 职责说明
API模块是系统的对外接口层，包含：
- DTO（数据传输对象）
- VO（视图对象）
- 服务接口定义
- OpenAPI/Swagger配置
- Feign Client接口

## 模块依赖
- 无外部依赖（最底层模块）
- 可被其他所有模块依赖

## 包结构
```
com.trek0.api/
├── dto/           # 数据传输对象
├── vo/            # 视图对象
├── service/       # 服务接口定义
├── security/      # 安全相关接口
└── model/         # 基础模型
```

## 使用方式
1. 其他模块通过依赖引入API接口
2. 服务实现类实现API中定义的接口
3. Web层使用API中的DTO进行数据传输

## 开发规范
- 所有接口方法使用DTO作为参数和返回值
- 避免在API层包含具体实现逻辑
- 保持接口的稳定性和向后兼容性 