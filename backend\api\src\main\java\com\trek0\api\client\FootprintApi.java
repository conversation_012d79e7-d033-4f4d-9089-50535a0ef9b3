package com.trek0.api.client;

import com.trek0.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 足迹服务API接口
 */
@FeignClient(name = "footprint-service", path = "/api/v1/footprints")
public interface FootprintApi {
    /**
     * 创建足迹
     */
    @PostMapping
    ApiResponse<FootprintDTO> createFootprint(@RequestBody CreateFootprintRequest request);
    /**
     * 获取足迹详情
     */
    @GetMapping("/{id}")
    ApiResponse<FootprintDTO> getFootprint(@PathVariable("id") String id);
    /**
     * 更新足迹
     */
    @PutMapping("/{id}")
    ApiResponse<FootprintDTO> updateFootprint(@PathVariable("id") String id, @RequestBody UpdateFootprintRequest request);
    /**
     * 删除足迹
     */
    @DeleteMapping("/{id}")
    ApiResponse<Void> deleteFootprint(@PathVariable("id") String id);
    /**
     * 分页查询足迹
     */
    @GetMapping
    ApiResponse<PageResponse<FootprintDTO>> getFootprints(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String visibility,
            @RequestParam(required = false) String tags);
    /**
     * 获取用户足迹
     */
    @GetMapping("/user/{userId}")
    ApiResponse<PageResponse<FootprintDTO>> getUserFootprints(
            @PathVariable("userId") Long userId,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size);
    /**
     * 获取附近足迹
     */
    @GetMapping("/nearby")
    ApiResponse<PageResponse<FootprintDTO>> getNearbyFootprints(
            @RequestParam Double lat,
            @RequestParam Double lng,
            @RequestParam(defaultValue = "5000") Double radius,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size);
}
