package com.trek0.api.client;

import com.trek0.api.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 足迹路径服务API接口
 */
@FeignClient(name = "footprint-path-service", path = "/api/v1/footprints/{footprintId}/paths")
public interface FootprintPathApi {
    /**
     * 添加路径
     */
    @PostMapping
    ApiResponse<FootprintPathDTO> addPath(@PathVariable("footprintId") String footprintId, @RequestBody CreatePathRequest request);
    /**
     * 获取足迹的所有路径
     */
    @GetMapping
    ApiResponse<PageResponse<FootprintPathDTO>> getFootprintPaths(@PathVariable("footprintId") String footprintId, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "20") Integer size);
    /**
     * 获取路径详情
     */
    @GetMapping("/{pathId}")
    ApiResponse<FootprintPathDTO> getPath(@PathVariable("footprintId") String footprintId, @PathVariable("pathId") Long pathId);
    /**
     * 删除路径
     */
    @DeleteMapping("/{pathId}")
    ApiResponse<Void> deletePath(@PathVariable("footprintId") String footprintId, @PathVariable("pathId") Long pathId);
}
