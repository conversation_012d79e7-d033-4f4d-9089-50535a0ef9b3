-- 企业标准用户关注关系表 - 重构版本

CREATE TABLE user_follows (
    follow_id BIGSERIAL PRIMARY KEY,
    follower_user_id BIGINT NOT NULL,
    following_user_id BIGINT NOT NULL,
    follow_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    followed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    unfollowed_at TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_follower_user FOREIGN KEY (follower_user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    CONSTRAINT fk_following_user FOREIGN KEY (following_user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_not_self_follow CHECK (follower_user_id != following_user_id),
    CONSTRAINT chk_follow_status CHECK (follow_status IN ('ACTIVE', 'BLOCKED', 'REMOVED')),
    CONSTRAINT uk_user_follow_pair UNIQUE (follower_user_id, following_user_id)
);

-- 企业级索引
CREATE INDEX idx_user_follows_follower ON user_follows(follower_user_id, follow_status);
CREATE INDEX idx_user_follows_following ON user_follows(following_user_id, follow_status);
CREATE INDEX idx_user_follows_followed_at ON user_follows(followed_at DESC);

-- 企业级注释
COMMENT ON TABLE user_follows IS '企业标准用户关注关系表';
COMMENT ON COLUMN user_follows.follow_id IS '关注关系主键ID';
COMMENT ON COLUMN user_follows.follower_user_id IS '关注者用户ID';
COMMENT ON COLUMN user_follows.following_user_id IS '被关注者用户ID';
COMMENT ON COLUMN user_follows.follow_status IS '关注状态：ACTIVE-活跃, BLOCKED-屏蔽, REMOVED-已取消';
COMMENT ON COLUMN user_follows.followed_at IS '关注时间';
COMMENT ON COLUMN user_follows.unfollowed_at IS '取消关注时间';