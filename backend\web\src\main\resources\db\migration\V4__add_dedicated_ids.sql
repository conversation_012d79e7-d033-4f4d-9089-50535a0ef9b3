-- 添加足迹和路径的专属ID字段
-- 为足迹表添加专属ID字段
ALTER TABLE footprints ADD COLUMN footprint_id VARCHAR(30) UNIQUE;

-- 为路径表添加专属ID字段
ALTER TABLE footprint_paths ADD COLUMN path_id VARCHAR(40) UNIQUE;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_footprints_footprint_id ON footprints(footprint_id);
CREATE INDEX IF NOT EXISTS idx_footprint_paths_path_id ON footprint_paths(path_id);

-- 添加注释
COMMENT ON COLUMN footprints.footprint_id IS '足迹专属ID，格式：FP-{timestamp}-{sequence}-{checksum}';
COMMENT ON COLUMN footprint_paths.path_id IS '路径专属ID，格式：PT-{footprint_short_id}-{timestamp}-{sequence}-{checksum}';