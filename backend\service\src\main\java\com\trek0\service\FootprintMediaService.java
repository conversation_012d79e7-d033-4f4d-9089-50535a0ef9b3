package com.trek0.service;

import com.trek0.api.dto.FootprintDTO;
import com.trek0.domain.model.User;

import java.util.List;

/**
 * 足迹媒体服务接口（标签和照片管理）
 */
public interface FootprintMediaService {
    
    /**
     * 添加标签
     */
    FootprintDTO addTag(User user, Long footprintId, String tag);
    
    /**
     * 移除标签
     */
    FootprintDTO removeTag(User user, Long footprintId, String tag);
    
    /**
     * 获取足迹的所有标签
     */
    List<String> getFootprintTags(Long footprintId);
    
    /**
     * 添加照片
     */
    FootprintDTO addPhoto(User user, Long footprintId, String photoUrl);
    
    /**
     * 移除照片
     */
    FootprintDTO removePhoto(User user, Long footprintId, String photoUrl);
    
    /**
     * 获取足迹的所有照片
     */
    List<String> getFootprintPhotos(Long footprintId);
}