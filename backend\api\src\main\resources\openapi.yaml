openapi: 3.0.1
info:
  title: Trek0 API
  version: 1.0.0
paths:
  /api/v1/users/{id}:
    get:
      summary: 获取用户信息
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDTO'
components:
  schemas:
    UserDTO:
      type: object
      properties:
        id:
          type: integer
        username:
          type: string
        email:
          type: string
        profileImage:
          type: string 