name: Java CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Build with <PERSON>ven
        run: mvn clean install -DskipTests=false
      - name: Run Checkstyle
        run: mvn checkstyle:check
      - name: Run Spotless
        run: mvn spotless:check
      - name: Run Tests and Coverage
        run: mvn test jacoco:report
      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: target/site/jacoco 