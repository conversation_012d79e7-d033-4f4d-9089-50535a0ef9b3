package com.trek0.web.controller;

import com.trek0.api.dto.*;
import com.trek0.api.dto.FootprintDTO;
import com.trek0.api.dto.FootprintPathDTO;
import com.trek0.service.FootprintService;
import com.trek0.service.FootprintPathService;
import com.trek0.service.UserService;
import com.trek0.service.AuditService; //  企业级安全：审计服务
import com.trek0.common.metrics.BusinessMetrics;
import com.trek0.web.controller.base.BaseController;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Timer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import jakarta.servlet.http.HttpServletRequest;
import com.trek0.api.dto.UserDTO;
import com.trek0.domain.model.Footprint;
import com.trek0.domain.model.User;
import java.util.stream.Collectors;
// 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖

/**
 * 足迹核心管理控制器（CRUD操作）
 */
@RestController
@RequestMapping("/api/v1/footprints")
@CrossOrigin(origins = {"http://localhost:5173", "http://localhost:3000"})
@Tag(name = "足迹管理", description = "足迹相关API")
public class FootprintController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(FootprintController.class);
    
    private final FootprintService footprintService;
    private final FootprintPathService footprintPathService;
    private final BusinessMetrics businessMetrics;
    private final AuditService auditService; //  企业级安全：审计服务
    // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖

    @Autowired
    public FootprintController(FootprintService footprintService, 
                              FootprintPathService footprintPathService,
                              UserService userService,
                              BusinessMetrics businessMetrics,
                              AuditService auditService) {
        super(userService);
        this.footprintService = footprintService;
        this.footprintPathService = footprintPathService;
        this.businessMetrics = businessMetrics;
        this.auditService = auditService;
        // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
    }
    
    /**
     * 创建新的足迹
     */
    @PostMapping
    @Operation(summary = "创建足迹", description = "创建新的足迹记录")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "未授权")
    })
    public ResponseEntity<?> createFootprint(
            @Parameter(description = "创建足迹请求", required = true)
            @Valid @RequestBody CreateFootprintRequest request,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        
        Timer.Sample timer = (Timer.Sample) businessMetrics.startFootprintCreationTimer();
        
        try {
            User currentUser = getCurrentUserEntity();
            FootprintDTO footprint = footprintService.createFootprint(currentUser, request);
            
            // 记录业务指标
            businessMetrics.incrementFootprintCreation();
            
            return ResponseEntity.ok(createSuccessResponse("足迹创建成功", footprint));
        } catch (Exception e) {
            logger.error("创建足迹失败", e);
            businessMetrics.incrementApiError("footprint_creation_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        } finally {
            businessMetrics.recordFootprintCreation(timer);
        }
    }
    
    /**
     * 获取当前用户的足迹列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的足迹", description = "获取当前用户的足迹列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未授权")
    })
    public ResponseEntity<?> getMyFootprints(
            @Parameter(description = "页码", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr,
            HttpServletRequest request) {
        try {
            User currentUser = getCurrentUserEntity();
            
            //  企业级安全：验证用户身份
            if (currentUser == null) {
                logger.warn("未授权访问：用户未登录");
                auditService.logSecurityException("UNAUTHORIZED_ACCESS", "用户未登录", 
                    getClientIpAddress(request), request.getHeader("User-Agent"));
                return ResponseEntity.status(401).body(createErrorResponse("用户未登录"));
            }
            
            logger.info("用户 {} 正在获取自己的足迹列表", currentUser.getUsername());
            
            //  企业级安全：记录数据访问审计
            auditService.logDataAccess(currentUser, "GET", "footprints", getClientIpAddress(request), request.getHeader("User-Agent"));
            
            Pageable pageable = PageRequest.of(page, size);
            Page<FootprintDTO> footprints = footprintService.getUserFootprints(currentUser, pageable);
            
            //  企业级安全：验证返回的足迹确实属于当前用户
            boolean hasUnauthorizedFootprints = footprints.getContent().stream()
                .anyMatch(fp -> !currentUser.getId().equals(fp.getUser().getId()));
            
            if (hasUnauthorizedFootprints) {
                logger.error("安全警告：检测到非当前用户的足迹数据，用户ID: {}", currentUser.getId());
                auditService.logSecurityException("DATA_LEAKAGE", "检测到非当前用户的足迹数据", 
                    getClientIpAddress(request), request.getHeader("User-Agent"));
                businessMetrics.incrementApiError("unauthorized_footprint_access");
                return ResponseEntity.status(403).body(createErrorResponse("数据访问权限错误"));
            }
            
            logger.info("用户 {} 成功获取 {} 个足迹", currentUser.getUsername(), footprints.getTotalElements());
            
            //  企业级安全：记录API调用审计
            auditService.logApiCall(currentUser, "GET", "/api/footprints/my", "127.0.0.1", 200, 100);
            
            return ResponseEntity.ok(footprints);
        } catch (Exception e) {
            logger.error("获取用户足迹失败", e);
            businessMetrics.incrementApiError("get_my_footprints_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 获取指定用户的足迹列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "获取用户足迹", description = "获取指定用户的足迹列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    public ResponseEntity<?> getUserFootprints(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "页码", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") int size) {
        try {
            User user = userService.findUserById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
            Pageable pageable = PageRequest.of(page, size);
            Page<FootprintDTO> footprints = footprintService.getUserFootprints(user, pageable);
            return ResponseEntity.ok(footprints);
        } catch (Exception e) {
            logger.error("获取用户足迹失败", e);
            businessMetrics.incrementApiError("get_user_footprints_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 获取公开的足迹列表
     */
    @GetMapping("/public")
    @Operation(summary = "获取公开足迹", description = "获取所有公开的足迹列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    public ResponseEntity<?> getPublicFootprints(
            @Parameter(description = "页码", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<FootprintDTO> footprints = footprintService.getPublicFootprints(pageable);
            return ResponseEntity.ok(footprints);
        } catch (Exception e) {
            logger.error("获取公开足迹失败", e);
            businessMetrics.incrementApiError("get_public_footprints_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 获取附近的足迹
     */
    @GetMapping("/nearby")
    @Operation(summary = "获取附近足迹", description = "根据地理位置获取附近的足迹")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    public ResponseEntity<?> getNearbyFootprints(
            @Parameter(description = "纬度", required = true, example = "39.9042")
            @RequestParam Double lat,
            @Parameter(description = "经度", required = true, example = "116.4074")
            @RequestParam Double lng,
            @Parameter(description = "搜索半径(公里)", example = "5")
            @RequestParam(defaultValue = "5") Double radius) {
        try {
            List<FootprintDTO> footprints = footprintService.getNearbyFootprints(lat, lng, radius);
            return ResponseEntity.ok(footprints);
        } catch (Exception e) {
            logger.error("获取附近足迹失败", e);
            businessMetrics.incrementApiError("get_nearby_footprints_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 搜索当前用户的足迹
     */
    @GetMapping("/my/search")
    @Operation(summary = "搜索我的足迹", description = "在当前用户的足迹中搜索")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "搜索成功"),
        @ApiResponse(responseCode = "401", description = "未授权")
    })
    public ResponseEntity<?> searchMyFootprints(
            @Parameter(description = "搜索关键词", required = true)
            @RequestParam String keyword,
            @Parameter(description = "页码", example = "0")
            @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            Pageable pageable = PageRequest.of(page, size);
            Page<FootprintDTO> footprints = footprintService.searchUserFootprints(currentUser, keyword, pageable);
            return ResponseEntity.ok(footprints);
        } catch (Exception e) {
            logger.error("搜索足迹失败", e);
            businessMetrics.incrementApiError("search_footprints_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 获取足迹详情
     */
    @GetMapping("/{footprintId}")
    @Operation(summary = "获取足迹详情", description = "根据ID获取足迹详细信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "足迹不存在")
    })
    public ResponseEntity<?> getFootprint(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            FootprintDTO dto = footprintService.getFootprint(footprintId, currentUser);
            
            // 记录足迹查看次数
            businessMetrics.incrementFootprintView();
            
            return ResponseEntity.ok(dto);
        } catch (Exception e) {
            logger.error("获取足迹详情失败", e);
            businessMetrics.incrementApiError("get_footprint_detail_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 更新足迹信息
     */
    @PutMapping("/{footprintId}")
    @Operation(summary = "更新足迹", description = "更新足迹信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "未授权"),
        @ApiResponse(responseCode = "404", description = "足迹不存在")
    })
    public ResponseEntity<?> updateFootprint(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @Parameter(description = "更新足迹请求", required = true)
            @Valid @RequestBody UpdateFootprintRequest request,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // footprintId 实际为 businessId，先查实体
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            FootprintDTO updated = footprintService.updateFootprint(currentUser, footprintId, request);
            return ResponseEntity.ok(createSuccessResponse("足迹更新成功", updated));
        } catch (Exception e) {
            logger.error("足迹更新失败", e);
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 删除足迹
     */
    @DeleteMapping("/{footprintId}")
    @Operation(summary = "删除足迹", description = "删除指定的足迹")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "401", description = "未授权"),
        @ApiResponse(responseCode = "404", description = "足迹不存在")
    })
    public ResponseEntity<?> deleteFootprint(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            footprintService.deleteFootprint(currentUser, footprintId);
            return ResponseEntity.ok(createSuccessResponse("删除足迹成功", null));
        } catch (Exception e) {
            logger.error("删除足迹失败", e);
            businessMetrics.incrementApiError("delete_footprint_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }

    // ========================= 路径管理API =========================
    
    /**
     * 获取足迹的所有路径
     */
    @GetMapping("/{footprintId}/paths")
    @Operation(summary = "获取足迹路径", description = "获取指定足迹的所有路径")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "足迹不存在")
    })
    public ResponseEntity<?> getFootprintPaths(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            List<FootprintPathDTO> paths = footprintPathService.getFootprintPaths(currentUser, footprintId);
            return ResponseEntity.ok(createSuccessResponse("获取路径列表成功", paths));
        } catch (Exception e) {
            logger.error("获取足迹路径失败: footprintId={}, error={}", footprintId, e.getMessage(), e);
            businessMetrics.incrementApiError("get_footprint_paths_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * 为足迹添加路径
     */
    @PostMapping("/{footprintId}/paths")
    @Operation(summary = "添加路径", description = "为指定足迹添加新路径")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "添加成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "未授权"),
        @ApiResponse(responseCode = "404", description = "足迹不存在")
    })
    public ResponseEntity<?> addPath(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @Parameter(description = "创建路径请求", required = true)
            @Valid @RequestBody CreatePathRequest request,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            FootprintPathDTO path = footprintPathService.addPathToFootprint(currentUser, footprintId, request);
            return ResponseEntity.ok(createSuccessResponse("路径添加成功", path));
        } catch (Exception e) {
            logger.error("添加路径失败: footprintId={}, error={}", footprintId, e.getMessage());
            businessMetrics.incrementApiError("add_path_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 获取路径详情（通过专属路径ID）
     */
    @GetMapping("/paths/{pathId}")
    @Operation(summary = "获取路径详情", description = "通过专属路径ID获取路径详细信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "路径不存在")
    })
    public ResponseEntity<?> getPathByDedicatedId(
            @Parameter(description = "专属路径ID", required = true)
            @PathVariable String pathId,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            FootprintPathDTO path = footprintPathService.getPath(pathId, currentUser);
            return ResponseEntity.ok(createSuccessResponse("获取路径详情成功", path));
        } catch (Exception e) {
            logger.error("获取路径详情失败: pathId={}, error={}", pathId, e.getMessage(), e);
            businessMetrics.incrementApiError("get_path_by_id_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 更新路径信息
     */
    @PutMapping("/paths/{pathId}")
    @Operation(summary = "更新路径", description = "更新指定路径的信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "未授权"),
        @ApiResponse(responseCode = "404", description = "路径不存在")
    })
    public ResponseEntity<?> updatePath(
            @Parameter(description = "路径ID", required = true)
            @PathVariable String pathId,
            @Parameter(description = "更新路径请求", required = true)
            @Valid @RequestBody CreatePathRequest request,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            Long pathIdLong = null;
            try { pathIdLong = Long.parseLong(pathId); } catch (Exception e) { return ResponseEntity.badRequest().body("路径ID格式错误"); }
            FootprintPathDTO updated = footprintPathService.updatePath(currentUser, pathIdLong, request);
            return ResponseEntity.ok(createSuccessResponse("路径更新成功", updated));
        } catch (Exception e) {
            logger.error("更新路径失败", e);
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 删除路径
     */
    @DeleteMapping("/paths/{pathId}")
    @Operation(summary = "删除路径", description = "删除指定的路径")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "401", description = "未授权"),
        @ApiResponse(responseCode = "404", description = "路径不存在")
    })
    public ResponseEntity<?> deletePath(
            @Parameter(description = "路径ID", required = true)
            @PathVariable String pathId,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            footprintPathService.deletePath(currentUser, pathId);
            return ResponseEntity.ok(createSuccessResponse("路径删除成功", null));
        } catch (Exception e) {
            logger.error("删除路径失败", e);
            businessMetrics.incrementApiError("delete_path_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }
    
    /**
     * 搜索足迹的路径
     */
    @GetMapping("/{footprintId}/paths/search")
    @Operation(summary = "搜索路径", description = "在指定足迹的路径中搜索")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "搜索成功")
    })
    public ResponseEntity<?> searchFootprintPaths(
            @Parameter(description = "足迹ID", required = true)
            @PathVariable Long footprintId,
            @Parameter(description = "搜索关键词", required = true)
            @RequestParam String keyword,
            @RequestHeader(value = "X-User-Id", required = false) String userIdStr) {
        try {
            User currentUser = getCurrentUserEntity();
            // 移除FootprintRepository、FootprintPathRepository、Footprint、FootprintPath等domain/infra依赖
            List<FootprintPathDTO> paths = footprintPathService.searchFootprintPaths(currentUser, footprintId, keyword);
            return ResponseEntity.ok(createSuccessResponse("搜索路径成功", paths));
        } catch (Exception e) {
            logger.error("搜索路径失败", e);
            businessMetrics.incrementApiError("search_paths_failed");
            return ResponseEntity.badRequest().body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * 获取客户端IP地址
     * 考虑代理和负载均衡器的情况
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    // 新增：获取当前用户实体
    private User getCurrentUserEntity() {
        UserDTO dto = getCurrentUser();
        if (dto == null) return null;
        User user = new User();
        user.setId(dto.getId());
        user.setUsername(dto.getUsername());
        user.setEmail(dto.getEmail());
        user.setProfileImageUrl(dto.getProfileImage());
        return user;
    }

    // 实体转DTO
    private FootprintDTO footprintToDTO(Footprint entity) {
        if (entity == null) return null;
        FootprintDTO dto = new FootprintDTO();
        dto.setId(entity.getId());
        dto.setFootprintId(entity.getBusinessId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        if (entity.getLatitude() != null && entity.getLongitude() != null) {
            FootprintDTO.LocationDTO loc = new FootprintDTO.LocationDTO(
                entity.getLatitude().doubleValue(),
                entity.getLongitude().doubleValue(),
                entity.getAddressText()
            );
            dto.setLocation(loc);
        }
        dto.setVisibility(entity.getVisibilityLevel() != null ? entity.getVisibilityLevel().name() : null);
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        // 用户摘要
        if (entity.getUser() != null) {
            dto.setUser(userToSummaryDTO(entity.getUser()));
        }
        // 可补充tags/photos/paths等映射
        return dto;
    }
    private FootprintDTO.UserSummaryDTO userToSummaryDTO(User user) {
        if (user == null) return null;
        return new FootprintDTO.UserSummaryDTO(user.getId(), user.getUsername(), user.getProfileImageUrl());
    }
    private List<FootprintDTO> footprintListToDTO(List<Footprint> list) {
        return list == null ? List.of() : list.stream().map(this::footprintToDTO).collect(Collectors.toList());
    }
    private Page<FootprintDTO> footprintPageToDTO(Page<Footprint> page) {
        return page.map(this::footprintToDTO);
    }
}