package com.trek0.domain.repository;

import com.trek0.domain.model.User;
import com.trek0.domain.model.UserLoginRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UserLoginRecordRepository extends JpaRepository<UserLoginRecord, Long> {
    
    /**
     * 根据用户查找登录记录
     */
    Page<UserLoginRecord> findByUserIdOrderByLoginTimeDesc(Long userId, Pageable pageable);
    
    /**
     * 根据IP地址查找登录记录
     */
    List<UserLoginRecord> findByIpAddressOrderByLoginTimeDesc(String ipAddress);
    
    /**
     * 根据时间范围查找登录记录
     */
    Page<UserLoginRecord> findByLoginTimeBetweenOrderByLoginTimeDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据用户和时间范围查找登录记录
     */
    Page<UserLoginRecord> findByUserIdAndLoginTimeBetweenOrderByLoginTimeDesc(Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计用户的登录次数
     */
    long countByUserId(Long userId);
    
    /**
     * 查找用户最近的登录记录
     */
    List<UserLoginRecord> findTop10ByUserIdOrderByLoginTimeDesc(Long userId);
    
    /**
     * 统计指定时间范围内的登录次数
     */
    @Query("SELECT COUNT(r) FROM UserLoginRecord r WHERE r.loginTime BETWEEN :startTime AND :endTime")
    long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

