# Spring Boot Actuator 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,circuitbreakers,retries,timelimiters,ratelimiters
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: GET,POST
    enabled-by-default: true
  
  endpoint:
    health:
      show-details: always
      show-components: always
      probes:
        enabled: true
    info:
      enabled: true
    metrics:
      enabled: true
    prometheus:
      enabled: true
  
  health:
    circuitbreakers:
      enabled: true
    ratelimiters:
      enabled: true
    diskspace:
      enabled: true
    db:
      enabled: true
  
  metrics:
    export:
      prometheus:
        enabled: true
        step: 10s
        descriptions: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
        resilience4j.circuitbreaker.calls: true
        resilience4j.retry.calls: true
        resilience4j.timelimiter.calls: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        resilience4j.circuitbreaker.calls: 0.5, 0.95, 0.99
        resilience4j.retry.calls: 0.5, 0.95, 0.99
        resilience4j.timelimiter.calls: 0.5, 0.95, 0.99
    tags:
      application: trek0-api
      environment: ${spring.profiles.active:development}
  
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
    git:
      mode: full
    build:
      enabled: true

# 应用信息配置
info:
  app:
    name: Trek0 API
    description: Trek0 足迹分享平台后端API
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}
  build:
    artifact: ${project.artifactId:trek0-api}
    name: ${project.name:Trek0 API}
    time: ${maven.build.timestamp:unknown}
    version: ${project.version:1.0.0}

# 日志配置
logging:
  level:
    io.github.resilience4j: DEBUG
    com.trek0.api.metrics: DEBUG
    com.trek0.api.health: DEBUG
    org.springframework.boot.actuator: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"