package com.trek0.domain.repository;

import com.trek0.domain.model.FootprintPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FootprintPathRepository extends JpaRepository<FootprintPath, Long> {
    
    /**
     * 根据专属路径ID查找路径
     */
    Optional<FootprintPath> findByPathId(Long pathId);
    
    /**
     * 查找某个足迹的所有路径
     */
    List<FootprintPath> findByFootprintIdOrderByCreatedAtAsc(Long footprintId);
    
    /**
     * 统计某个足迹的路径数量
     */
    long countByFootprintId(Long footprintId);
    
    /**
     * 根据关键词搜索足迹的路径
     */
    @Query("SELECT p FROM FootprintPath p WHERE p.footprint.id = :footprintId AND " +
           "(LOWER(p.pathName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.pathDescription) LIKE LOWER(CONCAT('%', :keyword, '%')))" +
           "ORDER BY p.createdAt ASC")
    List<FootprintPath> searchByFootprintIdAndKeyword(
        @Param("footprintId") Long footprintId, 
        @Param("keyword") String keyword
    );

    /**
     * 根据业务ID查找路径
     */
    Optional<FootprintPath> findByBusinessId(String businessId);
}