# Service Module

## 职责说明
Service模块是系统的业务逻辑层，包含：
- 业务服务接口定义
- 业务服务实现
- 业务规则和流程
- 事务管理
- 业务异常处理

## 模块依赖
- 依赖：`domain`、`api`、`common`、`config`模块
- 被依赖：`web`模块

## 包结构
```
com.trek0.service/
├── UserService.java           # 用户服务接口
├── FootprintService.java      # 足迹服务接口
├── VerificationService.java   # 验证服务接口
├── AuditService.java          # 审计服务接口
└── impl/                      # 服务实现
    ├── UserServiceImpl.java
    ├── FootprintServiceImpl.java
    └── ...
```

## 核心服务
- **UserService**: 用户管理业务逻辑
- **FootprintService**: 足迹管理业务逻辑
- **VerificationService**: 验证码业务逻辑
- **AuditService**: 审计日志业务逻辑

## 开发规范
- 接口与实现分离，便于Mock和扩展
- 业务逻辑集中在Service层
- 使用事务注解管理数据一致性
- 异常处理统一，抛出业务异常
- 依赖注入使用接口而非实现类 