package com.trek0.domain.repository;

import com.trek0.domain.model.FootprintTag;
import com.trek0.domain.model.Footprint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FootprintTagRepository extends JpaRepository<FootprintTag, Long> {
    
    /**
     * 根据足迹查找所有标签
     */
    List<FootprintTag> findByFootprintOrderByCreatedAtAsc(Footprint footprint);
    
    /**
     * 根据足迹ID查找所有标签
     */
    List<FootprintTag> findByFootprintFootprintIdOrderByCreatedAtAsc(Long footprintId);
    
    /**
     * 根据标签名称查找标签
     */
    List<FootprintTag> findByTagNameContainingIgnoreCase(String tagName);
    
    /**
     * 根据标签分类查找标签
     */
    List<FootprintTag> findByTagCategory(String tagCategory);
    
    /**
     * 查找足迹的特定标签
     */
    Optional<FootprintTag> findByFootprintAndTagName(Footprint footprint, String tagName);
    
    /**
     * 统计足迹的标签数量
     */
    long countByFootprint(Footprint footprint);
    
    /**
     * 查找热门标签
     */
    @Query("SELECT t.tagName, COUNT(t) as count FROM FootprintTag t GROUP BY t.tagName ORDER BY count DESC")
    List<Object[]> findPopularTags(@Param("limit") int limit);
    
    /**
     * 根据颜色查找标签
     */
    List<FootprintTag> findByTagColor(String tagColor);
    
    /**
     * 删除足迹的所有标签
     */
    void deleteByFootprint(Footprint footprint);
}