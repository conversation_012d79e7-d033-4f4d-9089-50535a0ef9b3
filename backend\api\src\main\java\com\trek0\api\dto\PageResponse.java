package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 分页响应DTO
 */
@Schema(description = "分页响应")
public class PageResponse<T> {
    
    @Schema(description = "数据列表")
    private List<T> content;
    
    @Schema(description = "当前页码", example = "0")
    private Integer page;
    
    @Schema(description = "每页大小", example = "20")
    private Integer size;
    
    @Schema(description = "总元素数", example = "100")
    private Long totalElements;
    
    @Schema(description = "总页数", example = "5")
    private Integer totalPages;
    
    @Schema(description = "是否为第一页")
    private Boolean first;
    
    @Schema(description = "是否为最后一页")
    private Boolean last;
    
    @Schema(description = "是否有下一页")
    private Boolean hasNext;
    
    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;

    public PageResponse() {}
    public PageResponse(List<T> content, Integer page, Integer size, Long totalElements, Integer totalPages, <PERSON><PERSON><PERSON> first, <PERSON><PERSON><PERSON> last, <PERSON><PERSON><PERSON> hasNext, Boolean hasPrevious) {
        this.content = content;
        this.page = page;
        this.size = size;
        this.totalElements = totalElements;
        this.totalPages = totalPages;
        this.first = first;
        this.last = last;
        this.hasNext = hasNext;
        this.hasPrevious = hasPrevious;
    }
    public List<T> getContent() { return content; }
    public void setContent(List<T> content) { this.content = content; }
    public Integer getPage() { return page; }
    public void setPage(Integer page) { this.page = page; }
    public Integer getSize() { return size; }
    public void setSize(Integer size) { this.size = size; }
    public Long getTotalElements() { return totalElements; }
    public void setTotalElements(Long totalElements) { this.totalElements = totalElements; }
    public Integer getTotalPages() { return totalPages; }
    public void setTotalPages(Integer totalPages) { this.totalPages = totalPages; }
    public Boolean getFirst() { return first; }
    public void setFirst(Boolean first) { this.first = first; }
    public Boolean getLast() { return last; }
    public void setLast(Boolean last) { this.last = last; }
    public Boolean getHasNext() { return hasNext; }
    public void setHasNext(Boolean hasNext) { this.hasNext = hasNext; }
    public Boolean getHasPrevious() { return hasPrevious; }
    public void setHasPrevious(Boolean hasPrevious) { this.hasPrevious = hasPrevious; }
    @Override
    public String toString() {
        return "PageResponse{" +
                "content=" + content +
                ", page=" + page +
                ", size=" + size +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", first=" + first +
                ", last=" + last +
                ", hasNext=" + hasNext +
                ", hasPrevious=" + hasPrevious +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PageResponse<?> that = (PageResponse<?>) o;
        return java.util.Objects.equals(content, that.content) &&
                java.util.Objects.equals(page, that.page) &&
                java.util.Objects.equals(size, that.size) &&
                java.util.Objects.equals(totalElements, that.totalElements) &&
                java.util.Objects.equals(totalPages, that.totalPages) &&
                java.util.Objects.equals(first, that.first) &&
                java.util.Objects.equals(last, that.last) &&
                java.util.Objects.equals(hasNext, that.hasNext) &&
                java.util.Objects.equals(hasPrevious, that.hasPrevious);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(content, page, size, totalElements, totalPages, first, last, hasNext, hasPrevious);
    }
    
    public static <T> PageResponse<T> of(List<T> content, Integer page, Integer size, Long totalElements) {
        PageResponse<T> response = new PageResponse<>();
        response.setContent(content);
        response.setPage(page);
        response.setSize(size);
        response.setTotalElements(totalElements);
        
        int totalPages = (int) Math.ceil((double) totalElements / size);
        response.setTotalPages(totalPages);
        response.setFirst(page == 0);
        response.setLast(page >= totalPages - 1);
        response.setHasNext(page < totalPages - 1);
        response.setHasPrevious(page > 0);
        
        return response;
    }
}
