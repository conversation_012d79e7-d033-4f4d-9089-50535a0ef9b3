package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "footprint_paths")
public class FootprintPath {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "path_id")
    private Long pathId;
    
    @Column(name = "business_id", unique = true, nullable = false, length = 50)
    private String businessId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "footprint_id", nullable = false)
    @JsonBackReference
    private Footprint footprint;
    
    @Column(name = "path_name", nullable = false, length = 200)
    @NotBlank(message = "Path name is required")
    @Size(min = 1, max = 200, message = "Path name must be between 1 and 200 characters")
    private String pathName;
    
    @Column(name = "path_description", columnDefinition = "TEXT")
    private String pathDescription;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "path_type", nullable = false, length = 30)
    private PathType pathType = PathType.WALKING;
    
    @Column(name = "total_distance_meters", precision = 10, scale = 2)
    @Min(value = 0, message = "Distance must be non-negative")
    private BigDecimal totalDistanceMeters;
    
    @Column(name = "total_duration_seconds")
    @Min(value = 0, message = "Duration must be non-negative")
    private Integer totalDurationSeconds;
    
    @Column(name = "elevation_gain_meters", precision = 8, scale = 2)
    @Min(value = 0, message = "Elevation gain must be non-negative")
    private BigDecimal elevationGainMeters;
    
    @Column(name = "elevation_loss_meters", precision = 8, scale = 2)
    @Min(value = 0, message = "Elevation loss must be non-negative")
    private BigDecimal elevationLossMeters;
    
    @Column(name = "max_elevation_meters", precision = 8, scale = 2)
    private BigDecimal maxElevationMeters;
    
    @Column(name = "min_elevation_meters", precision = 8, scale = 2)
    private BigDecimal minElevationMeters;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "path_difficulty", length = 20)
    private PathDifficulty pathDifficulty;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "path_status", nullable = false, length = 20)
    private PathStatus pathStatus = PathStatus.ACTIVE;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @OneToMany(mappedBy = "path", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<PathPoint> points = new ArrayList<>();
    
    // 路径类型枚举
    public enum PathType {
        WALKING, RUNNING, CYCLING, DRIVING, HIKING, CLIMBING, OTHER
    }
    
    // 路径难度枚举
    public enum PathDifficulty {
        EASY, MODERATE, HARD, EXTREME, UNKNOWN
    }
    
    // 路径状态枚举
    public enum PathStatus {
        ACTIVE, ARCHIVED, DELETED, DRAFT
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Constructors
    public FootprintPath() {}
    
    public FootprintPath(Footprint footprint, String pathName, PathType pathType) {
        this.footprint = footprint;
        this.pathName = pathName;
        this.pathType = pathType;
    }
    
    // Getters and Setters
    public Long getPathId() {
        return pathId;
    }
    
    public void setPathId(Long pathId) {
        this.pathId = pathId;
    }
    
    public String getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }
    
    public Footprint getFootprint() {
        return footprint;
    }
    
    public void setFootprint(Footprint footprint) {
        this.footprint = footprint;
    }
    
    public String getPathName() {
        return pathName;
    }
    
    public void setPathName(String pathName) {
        this.pathName = pathName;
    }
    
    public String getPathDescription() {
        return pathDescription;
    }
    
    public void setPathDescription(String pathDescription) {
        this.pathDescription = pathDescription;
    }
    
    public PathType getPathType() {
        return pathType;
    }
    
    public void setPathType(PathType pathType) {
        this.pathType = pathType;
    }
    
    public BigDecimal getTotalDistanceMeters() {
        return totalDistanceMeters;
    }
    
    public void setTotalDistanceMeters(BigDecimal totalDistanceMeters) {
        this.totalDistanceMeters = totalDistanceMeters;
    }
    
    public Integer getTotalDurationSeconds() {
        return totalDurationSeconds;
    }
    
    public void setTotalDurationSeconds(Integer totalDurationSeconds) {
        this.totalDurationSeconds = totalDurationSeconds;
    }
    
    public BigDecimal getElevationGainMeters() {
        return elevationGainMeters;
    }
    
    public void setElevationGainMeters(BigDecimal elevationGainMeters) {
        this.elevationGainMeters = elevationGainMeters;
    }
    
    public BigDecimal getElevationLossMeters() {
        return elevationLossMeters;
    }
    
    public void setElevationLossMeters(BigDecimal elevationLossMeters) {
        this.elevationLossMeters = elevationLossMeters;
    }
    
    public BigDecimal getMaxElevationMeters() {
        return maxElevationMeters;
    }
    
    public void setMaxElevationMeters(BigDecimal maxElevationMeters) {
        this.maxElevationMeters = maxElevationMeters;
    }
    
    public BigDecimal getMinElevationMeters() {
        return minElevationMeters;
    }
    
    public void setMinElevationMeters(BigDecimal minElevationMeters) {
        this.minElevationMeters = minElevationMeters;
    }
    
    public PathDifficulty getPathDifficulty() {
        return pathDifficulty;
    }
    
    public void setPathDifficulty(PathDifficulty pathDifficulty) {
        this.pathDifficulty = pathDifficulty;
    }
    
    public PathStatus getPathStatus() {
        return pathStatus;
    }
    
    public void setPathStatus(PathStatus pathStatus) {
        this.pathStatus = pathStatus;
    }
    
    public List<PathPoint> getPoints() {
        return points;
    }
    
    public void setPoints(List<PathPoint> points) {
        this.points = points;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // 向后兼容性方法
    public Long getId() {
        return pathId;
    }
    
    public void setId(Long id) {
        this.pathId = id;
    }
    
    // 为了向后兼容，保留原有的pathId字段访问方式（返回businessId）
    public String getPathIdString() {
        return businessId;
    }
    
    public void setPathIdString(String pathId) {
        this.businessId = pathId;
    }
    
    public String getName() {
        return pathName;
    }
    
    public void setName(String name) {
        this.pathName = name;
    }
    
    public String getDescription() {
        return pathDescription;
    }
    
    public void setDescription(String description) {
        this.pathDescription = description;
    }
    
    public PathType getType() {
        return pathType;
    }
    
    public void setType(PathType type) {
        this.pathType = type;
    }
    
    public Double getDistance() {
        return totalDistanceMeters != null ? totalDistanceMeters.doubleValue() : null;
    }
    
    public void setDistance(Double distance) {
        this.totalDistanceMeters = distance != null ? BigDecimal.valueOf(distance) : null;
    }
    
    public Integer getDuration() {
        return totalDurationSeconds;
    }
    
    public void setDuration(Integer duration) {
        this.totalDurationSeconds = duration;
    }
    
    // Helper methods
    public void addPoint(PathPoint point) {
        points.add(point);
        point.setPath(this);
        point.setPointSequence(points.size() - 1);
    }
    
    public void removePoint(PathPoint point) {
        points.remove(point);
        point.setPath(null);
        // 重新排序
        for (int i = 0; i < points.size(); i++) {
            points.get(i).setPointSequence(i);
        }
    }
    
    public void clearPoints() {
        points.clear();
    }
    
    // 业务方法
    public boolean isActive() {
        return pathStatus == PathStatus.ACTIVE;
    }
    
    public boolean isArchived() {
        return pathStatus == PathStatus.ARCHIVED;
    }
    
    public boolean isDeleted() {
        return pathStatus == PathStatus.DELETED;
    }
    
    public boolean isDraft() {
        return pathStatus == PathStatus.DRAFT;
    }
    
    public void activate() {
        this.pathStatus = PathStatus.ACTIVE;
    }
    
    public void archive() {
        this.pathStatus = PathStatus.ARCHIVED;
    }
    
    public void delete() {
        this.pathStatus = PathStatus.DELETED;
    }
    
    public void setAsDraft() {
        this.pathStatus = PathStatus.DRAFT;
    }
    
    // 计算总海拔变化
    public BigDecimal getTotalElevationChange() {
        if (elevationGainMeters != null && elevationLossMeters != null) {
            return elevationGainMeters.add(elevationLossMeters);
        }
        return null;
    }
    
    // 计算净海拔变化
    public BigDecimal getNetElevationChange() {
        if (elevationGainMeters != null && elevationLossMeters != null) {
            return elevationGainMeters.subtract(elevationLossMeters);
        }
        return null;
    }
    
    // 获取距离（公里）
    public BigDecimal getDistanceInKilometers() {
        if (totalDistanceMeters != null) {
            return totalDistanceMeters.divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
        }
        return null;
    }
    
    // 获取时长（分钟）
    public Integer getDurationInMinutes() {
        if (totalDurationSeconds != null) {
            return totalDurationSeconds / 60;
        }
        return null;
    }
    
    // 获取时长（小时）
    public Double getDurationInHours() {
        if (totalDurationSeconds != null) {
            return totalDurationSeconds / 3600.0;
        }
        return null;
    }
}