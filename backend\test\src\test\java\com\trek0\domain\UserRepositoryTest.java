package com.trek0.domain;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import com.trek0.infra.repository.UserRepositoryImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.*;

public class UserRepositoryTest {
    private UserRepository userRepository;
    @BeforeEach
    void setUp() { userRepository = new UserRepositoryImpl(); }
    @Test
    void saveAndFindById() {
        User user = new User();
        user.setUsername("repoTest");
        user.setEmail("<EMAIL>");
        user.setPassword("pwd");
        User saved = userRepository.save(user);
        Optional<User> found = userRepository.findById(saved.getId());
        assertTrue(found.isPresent());
        assertEquals("repoTest", found.get().getUsername());
    }
    @Test
    void findByEmail_notFound() {
        Optional<User> found = userRepository.findByEmail("<EMAIL>");
        assertFalse(found.isPresent());
    }
} 