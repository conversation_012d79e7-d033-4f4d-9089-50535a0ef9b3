package com.trek0.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "user_follows")
public class UserFollow {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "follow_id")
    private Long followId;
    
    @NotNull(message = "关注者不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "follower_id", nullable = false)
    private User follower;
    
    @NotNull(message = "被关注者不能为空")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "following_id", nullable = false)
    private User following;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "follow_status", nullable = false, length = 20)
    private FollowStatus followStatus = FollowStatus.ACTIVE;
    
    @Column(name = "followed_at", nullable = false)
    private LocalDateTime followedAt;
    
    @Column(name = "unfollowed_at")
    private LocalDateTime unfollowedAt;
    
    // 枚举定义
    public enum FollowStatus {
        ACTIVE,    // 活跃关注
        BLOCKED,   // 屏蔽
        REMOVED    // 已取消
    }
    
    @PrePersist
    protected void onCreate() {
        followedAt = LocalDateTime.now();
    }
    
    // Constructors
    public UserFollow() {}
    
    public UserFollow(User follower, User following) {
        this.follower = follower;
        this.following = following;
    }
    
    public UserFollow(User follower, User following, FollowStatus followStatus) {
        this.follower = follower;
        this.following = following;
        this.followStatus = followStatus;
    }
    
    // Getters and Setters
    public Long getFollowId() {
        return followId;
    }
    
    public void setFollowId(Long followId) {
        this.followId = followId;
    }
    
    public User getFollower() {
        return follower;
    }
    
    public void setFollower(User follower) {
        this.follower = follower;
    }
    
    public User getFollowing() {
        return following;
    }
    
    public void setFollowing(User following) {
        this.following = following;
    }
    
    public FollowStatus getFollowStatus() {
        return followStatus;
    }
    
    public void setFollowStatus(FollowStatus followStatus) {
        this.followStatus = followStatus;
    }
    
    public LocalDateTime getFollowedAt() {
        return followedAt;
    }
    
    public void setFollowedAt(LocalDateTime followedAt) {
        this.followedAt = followedAt;
    }
    
    public LocalDateTime getUnfollowedAt() {
        return unfollowedAt;
    }
    
    public void setUnfollowedAt(LocalDateTime unfollowedAt) {
        this.unfollowedAt = unfollowedAt;
    }
    
    // 向后兼容性方法
    public Long getId() {
        return followId;
    }
    
    public void setId(Long id) {
        this.followId = id;
    }
    
    public User getFollowerUser() {
        return follower;
    }
    
    public void setFollowerUser(User followerUser) {
        this.follower = followerUser;
    }
    
    public User getFollowingUser() {
        return following;
    }
    
    public void setFollowingUser(User followingUser) {
        this.following = followingUser;
    }
    
    public String getStatus() {
        return followStatus != null ? followStatus.name() : null;
    }
    
    public void setStatus(String status) {
        if (status != null) {
            this.followStatus = FollowStatus.valueOf(status.toUpperCase());
        }
    }
    
    public LocalDateTime getCreatedAt() {
        return followedAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.followedAt = createdAt;
    }
    
    // 业务方法
    public boolean isActive() {
        return followStatus == FollowStatus.ACTIVE;
    }
    
    public boolean isBlocked() {
        return followStatus == FollowStatus.BLOCKED;
    }
    
    public boolean isRemoved() {
        return followStatus == FollowStatus.REMOVED;
    }
    
    public void activate() {
        this.followStatus = FollowStatus.ACTIVE;
        this.unfollowedAt = null;
    }
    
    public void block() {
        this.followStatus = FollowStatus.BLOCKED;
        this.unfollowedAt = LocalDateTime.now();
    }
    
    public void remove() {
        this.followStatus = FollowStatus.REMOVED;
        this.unfollowedAt = LocalDateTime.now();
    }
    
    public void unfollow() {
        remove();
    }
    
    // 验证关注关系是否有效
    public boolean isValidFollow() {
        return follower != null && following != null && 
               !follower.equals(following) && isActive();
    }
    
    // 获取关注持续天数
    public Long getFollowDurationDays() {
        if (followedAt == null) return null;
        
        LocalDateTime endTime = unfollowedAt != null ? unfollowedAt : LocalDateTime.now();
        return java.time.Duration.between(followedAt, endTime).toDays();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserFollow)) return false;
        
        UserFollow that = (UserFollow) o;
        return followId != null && followId.equals(that.followId);
    }
    
    @Override
    public int hashCode() {
        return followId != null ? followId.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return "UserFollow{" +
                "followId=" + followId +
                ", follower=" + (follower != null ? follower.getUsername() : null) +
                ", following=" + (following != null ? following.getUsername() : null) +
                ", followStatus=" + followStatus +
                ", followedAt=" + followedAt +
                '}';
    }
}


