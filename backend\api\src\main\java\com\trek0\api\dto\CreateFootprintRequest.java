package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.List;
import java.util.Set;

/**
 * 创建足迹请求DTO
 */
@Schema(description = "创建足迹请求")
public class CreateFootprintRequest {
    @NotBlank(message = "足迹名称不能为空")
    @Size(max = 100, message = "足迹名称不能超过100个字符")
    @Schema(description = "足迹名称", required = true)
    private String name;
    @Size(max = 500, message = "足迹描述不能超过500个字符")
    @Schema(description = "足迹描述")
    private String description;
    @NotNull(message = "位置信息不能为空")
    @Schema(description = "位置信息", required = true)
    private LocationRequest location;
    @Schema(description = "标签列表")
    private Set<String> tags;
    @Schema(description = "照片列表")
    private List<String> photos;
    @Schema(description = "可见性", defaultValue = "PUBLIC")
    private String visibility = "PUBLIC";
    @Schema(description = "位置选择方式")
    private String locationMode;
    public CreateFootprintRequest() {}
    // getter/setter
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public LocationRequest getLocation() { return location; }
    public void setLocation(LocationRequest location) { this.location = location; }
    public Set<String> getTags() { return tags; }
    public void setTags(Set<String> tags) { this.tags = tags; }
    public List<String> getPhotos() { return photos; }
    public void setPhotos(List<String> photos) { this.photos = photos; }
    public String getVisibility() { return visibility; }
    public void setVisibility(String visibility) { this.visibility = visibility; }
    public String getLocationMode() { return locationMode; }
    public void setLocationMode(String locationMode) { this.locationMode = locationMode; }
    @Override
    public String toString() {
        return "CreateFootprintRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", location=" + location +
                ", tags=" + tags +
                ", photos=" + photos +
                ", visibility='" + visibility + '\'' +
                ", locationMode='" + locationMode + '\'' +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateFootprintRequest that = (CreateFootprintRequest) o;
        return java.util.Objects.equals(name, that.name) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(location, that.location) &&
                java.util.Objects.equals(tags, that.tags) &&
                java.util.Objects.equals(photos, that.photos) &&
                java.util.Objects.equals(visibility, that.visibility) &&
                java.util.Objects.equals(locationMode, that.locationMode);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(name, description, location, tags, photos, visibility, locationMode);
    }
    /**
     * 位置请求内部类
     */
    @Schema(description = "位置请求信息")
    public static class LocationRequest {
        @NotNull(message = "纬度不能为空")
        @Schema(description = "纬度", required = true)
        private Double lat;
        @NotNull(message = "经度不能为空")
        @Schema(description = "经度", required = true)
        private Double lng;
        @Size(max = 255, message = "地址不能超过255个字符")
        @Schema(description = "地址")
        private String address;
        public LocationRequest() {}
        public LocationRequest(Double lat, Double lng, String address) {
            this.lat = lat;
            this.lng = lng;
            this.address = address;
        }
        public Double getLat() { return lat; }
        public void setLat(Double lat) { this.lat = lat; }
        public Double getLng() { return lng; }
        public void setLng(Double lng) { this.lng = lng; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        @Override
        public String toString() {
            return "LocationRequest{" +
                    "lat=" + lat +
                    ", lng=" + lng +
                    ", address='" + address + '\'' +
                    '}';
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            LocationRequest that = (LocationRequest) o;
            return java.util.Objects.equals(lat, that.lat) &&
                    java.util.Objects.equals(lng, that.lng) &&
                    java.util.Objects.equals(address, that.address);
        }
        @Override
        public int hashCode() {
            return java.util.Objects.hash(lat, lng, address);
        }
    }
}