# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      database:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        automatic-transition-from-open-to-half-open-enabled: true
        wait-duration-in-open-state: 5s
        failure-rate-threshold: 50  # 降低失败率阈值从99%到50%
        event-consumer-buffer-size: 10
        record-exceptions:
          - org.springframework.dao.DataAccessException
          - java.sql.SQLException
          - jakarta.persistence.PersistenceException
      external-api:
        register-health-indicator: true
        sliding-window-size: 20
        minimum-number-of-calls: 10
        permitted-number-of-calls-in-half-open-state: 5
        automatic-transition-from-open-to-half-open-enabled: true
        wait-duration-in-open-state: 10s
        failure-rate-threshold: 60  # 降低失败率阈值从99%到60%
        event-consumer-buffer-size: 10
        record-exceptions:
          - java.io.IOException
          - java.net.SocketTimeoutException
          - org.springframework.web.client.ResourceAccessException

  retry:
    instances:
      database:
        max-attempts: 3
        wait-duration: 500ms
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - org.springframework.dao.DataAccessException
          - java.sql.SQLException
          - jakarta.persistence.PersistenceException
      external-api:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
        retry-exceptions:
          - java.io.IOException
          - java.net.SocketTimeoutException
          - org.springframework.web.client.ResourceAccessException

  timelimiter:
    instances:
      default:
        timeout-duration: 3s
        cancel-running-future: true
      long-running:
        timeout-duration: 10s
        cancel-running-future: true

  ratelimiter:
    instances:
      api:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 0s
      user-registration:
        limit-for-period: 10
        limit-refresh-period: 1m
        timeout-duration: 0s
      password-reset:
        limit-for-period: 5
        limit-refresh-period: 1m
        timeout-duration: 0s

# Management endpoints for monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,circuitbreakers,ratelimiters,retries,timelimiters
  endpoint:
    health:
      show-details: always
  health:
    circuitbreakers:
      enabled: true
    ratelimiters:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
        "[resilience4j.circuitbreaker.calls]": true
        "[resilience4j.retry.calls]": true
        "[resilience4j.timelimiter.calls]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
        "[resilience4j.circuitbreaker.calls]": 0.5, 0.95, 0.99
        "[resilience4j.retry.calls]": 0.5, 0.95, 0.99
        "[resilience4j.timelimiter.calls]": 0.5, 0.95, 0.99

# Logging configuration for resilience4j
logging:
  level:
    io.github.resilience4j: DEBUG
    com.trek0.api.metrics: DEBUG
    com.trek0.api.service: DEBUG