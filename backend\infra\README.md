# Infra Module

## 职责说明
Infra模块是系统的基础设施层，包含：
- 数据库访问实现
- 缓存实现
- 消息队列实现
- 第三方API集成
- 文件存储实现
- 外部服务调用

## 模块依赖
- 依赖：`domain`、`common`、`config`模块
- 被依赖：`service`模块

## 包结构
```
com.trek0.infra/
├── repository/      # Repository实现
│   ├── UserRepositoryImpl.java
│   ├── FootprintRepositoryImpl.java
│   └── ...
├── cache/           # 缓存实现
├── mq/              # 消息队列
├── external/        # 外部服务
├── storage/         # 文件存储
└── config/          # 基础设施配置
```

## 核心实现
- **UserRepositoryImpl**: 用户数据访问实现
- **FootprintRepositoryImpl**: 足迹数据访问实现
- **RedisCacheManager**: Redis缓存管理
- **FileStorageService**: 文件存储服务

## 开发规范
- 实现domain层定义的Repository接口
- 使用Spring Data JPA进行数据访问
- 缓存使用Redis
- 外部服务调用使用RestTemplate或Feign
- 文件存储支持本地和云存储
- 异常处理统一，转换为领域异常 