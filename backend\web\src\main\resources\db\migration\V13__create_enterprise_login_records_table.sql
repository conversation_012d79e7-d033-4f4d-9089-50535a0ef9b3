-- 企业标准用户登录记录表 - 重构版本

CREATE TABLE user_login_records (
    login_record_id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    session_uuid VARCHAR(36) UNIQUE NOT NULL,
    logged_in_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    logged_out_at TIMESTAMP,
    login_ip_address INET NOT NULL,
    login_ip_location VARCHAR(255),
    device_fingerprint VARCHAR(255),
    user_agent_string TEXT,
    browser_name VARCHAR(100),
    browser_version VARCHAR(50),
    os_name VARCHAR(100),
    os_version VARCHAR(50),
    device_type VARCHAR(50),
    login_method VARCHAR(50) NOT NULL DEFAULT 'PASSWORD',
    login_status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS',
    failure_reason VARCHAR(255),
    session_duration_seconds INTEGER,
    
    -- 外键约束
    CONSTRAINT fk_login_user FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_login_method CHECK (login_method IN ('PASSWORD', 'OAUTH', 'SSO', 'API_KEY')),
    CONSTRAINT chk_login_status CHECK (login_status IN ('SUCCESS', 'FAILED', 'BLOCKED', 'EXPIRED')),
    CONSTRAINT chk_device_type CHECK (device_type IN ('DESKTOP', 'MOBILE', 'TABLET', 'API', 'UNKNOWN'))
);

-- 企业级索引策略
CREATE INDEX idx_login_records_user_id ON user_login_records(user_id, logged_in_at DESC);
CREATE INDEX idx_login_records_ip ON user_login_records(login_ip_address);
CREATE INDEX idx_login_records_session ON user_login_records(session_uuid);
CREATE INDEX idx_login_records_time ON user_login_records(logged_in_at DESC);
CREATE INDEX idx_login_records_status ON user_login_records(login_status);
CREATE INDEX idx_login_records_method ON user_login_records(login_method);

-- 企业级注释
COMMENT ON TABLE user_login_records IS '企业标准用户登录记录表 - 安全审计';
COMMENT ON COLUMN user_login_records.login_record_id IS '登录记录主键ID';
COMMENT ON COLUMN user_login_records.user_id IS '用户ID（外键）';
COMMENT ON COLUMN user_login_records.session_uuid IS '会话唯一标识符';
COMMENT ON COLUMN user_login_records.logged_in_at IS '登录时间';
COMMENT ON COLUMN user_login_records.logged_out_at IS '登出时间';
COMMENT ON COLUMN user_login_records.login_ip_address IS '登录IP地址';
COMMENT ON COLUMN user_login_records.login_ip_location IS '登录IP地理位置';
COMMENT ON COLUMN user_login_records.device_fingerprint IS '设备指纹信息';
COMMENT ON COLUMN user_login_records.user_agent_string IS '完整用户代理字符串';
COMMENT ON COLUMN user_login_records.browser_name IS '浏览器名称';
COMMENT ON COLUMN user_login_records.browser_version IS '浏览器版本';
COMMENT ON COLUMN user_login_records.os_name IS '操作系统名称';
COMMENT ON COLUMN user_login_records.os_version IS '操作系统版本';
COMMENT ON COLUMN user_login_records.device_type IS '设备类型';
COMMENT ON COLUMN user_login_records.login_method IS '登录方式';
COMMENT ON COLUMN user_login_records.login_status IS '登录状态';
COMMENT ON COLUMN user_login_records.failure_reason IS '登录失败原因';
COMMENT ON COLUMN user_login_records.session_duration_seconds IS '会话持续时间（秒）';