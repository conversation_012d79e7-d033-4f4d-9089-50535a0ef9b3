package com.trek0.infra.repository;

import com.trek0.domain.model.Footprint;
import com.trek0.domain.repository.FootprintRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 足迹JPA仓储实现
 */
@Repository
public interface FootprintJpaRepository extends JpaRepository<Footprint, Long>, FootprintRepository {
    /**
     * 根据用户ID查找足迹
     */
    List<Footprint> findByUserId(Long userId);
    /**
     * 查找用户公开足迹
     */
    @Query("SELECT f FROM Footprint f WHERE f.user.id = :userId AND f.visibilityLevel = 'PUBLIC'")
    List<Footprint> findPublicFootprintsByUserId(@Param("userId") Long userId);
    /**
     * 查找所有公开足迹
     */
    @Query("SELECT f FROM Footprint f WHERE f.visibilityLevel = 'PUBLIC' ORDER BY f.createdAt DESC")
    List<Footprint> findPublicFootprints();
    /**
     * 查找指定时间后的足迹
     */
    @Query("SELECT f FROM Footprint f WHERE f.createdAt >= :since")
    List<Footprint> findFootprintsCreatedSince(@Param("since") LocalDateTime since);
    /**
     * 统计用户足迹数量
     */
    @Query("SELECT COUNT(f) FROM Footprint f WHERE f.user.id = :userId")
    long countByUserId(@Param("userId") Long userId);
}