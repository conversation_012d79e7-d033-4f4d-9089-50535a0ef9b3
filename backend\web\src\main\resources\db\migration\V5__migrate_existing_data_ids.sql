-- 为现有足迹和路径生成专属ID
-- 注意：这个脚本需要在应用程序中运行，因为需要使用Java的ID生成器

-- 临时存储过程，用于生成简单的专属ID（仅用于迁移）
CREATE OR REPLACE FUNCTION generate_temp_footprint_id(footprint_db_id BIGINT) 
RETURNS VARCHAR(30) AS $$
DECLARE
    timestamp_part VARCHAR(10);
    sequence_part VARCHAR(4);
    checksum_part VARCHAR(2);
BEGIN
    -- 使用当前时间戳的后10位
    timestamp_part := LPAD((EXTRACT(EPOCH FROM NOW())::BIGINT % 10000000000)::TEXT, 10, '0');
    
    -- 使用足迹ID作为序列号（取后4位）
    sequence_part := LPAD((footprint_db_id % 10000)::TEXT, 4, '0');
    
    -- 简单校验码（时间戳和序列号的和取模100）
    checksum_part := LPAD(((EXTRACT(EPOCH FROM NOW())::BIGINT + footprint_db_id) % 100)::TEXT, 2, '0');
    
    RETURN 'FP-' || timestamp_part || '-' || sequence_part || '-' || checksum_part;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION generate_temp_path_id(path_db_id BIGINT, footprint_short_id VARCHAR(6)) 
RETURNS VARCHAR(40) AS $$
DECLARE
    timestamp_part VARCHAR(10);
    sequence_part VARCHAR(4);
    checksum_part VARCHAR(2);
BEGIN
    -- 使用当前时间戳的后10位
    timestamp_part := LPAD((EXTRACT(EPOCH FROM NOW())::BIGINT % 10000000000)::TEXT, 10, '0');
    
    -- 使用路径ID作为序列号（取后4位）
    sequence_part := LPAD((path_db_id % 10000)::TEXT, 4, '0');
    
    -- 简单校验码
    checksum_part := LPAD(((EXTRACT(EPOCH FROM NOW())::BIGINT + path_db_id) % 100)::TEXT, 2, '0');
    
    RETURN 'PT-' || footprint_short_id || '-' || timestamp_part || '-' || sequence_part || '-' || checksum_part;
END;
$$ LANGUAGE plpgsql;

-- 为现有足迹生成专属ID
UPDATE footprints 
SET footprint_id = generate_temp_footprint_id(id)
WHERE footprint_id IS NULL;

-- 为现有路径生成专属ID
-- 首先需要获取足迹的短ID（从专属ID中提取）
UPDATE footprint_paths 
SET path_id = generate_temp_path_id(
    footprint_paths.id, 
    SUBSTRING(footprints.footprint_id FROM 4 FOR 6)  -- 提取FP-后面的前6位作为短ID
)
FROM footprints 
WHERE footprint_paths.footprint_id = footprints.id 
  AND footprint_paths.path_id IS NULL
  AND footprints.footprint_id IS NOT NULL;

-- 清理临时函数
DROP FUNCTION IF EXISTS generate_temp_footprint_id(BIGINT);
DROP FUNCTION IF EXISTS generate_temp_path_id(BIGINT, VARCHAR(6));

-- 添加注释说明这是临时迁移生成的ID
COMMENT ON COLUMN footprints.footprint_id IS '足迹专属ID，格式：FP-{timestamp}-{sequence}-{checksum}。现有数据使用迁移脚本生成。';
COMMENT ON COLUMN footprint_paths.path_id IS '路径专属ID，格式：PT-{footprint_short_id}-{timestamp}-{sequence}-{checksum}。现有数据使用迁移脚本生成。';