# Config Module

## 职责说明
Config模块是系统的配置层，包含：
- Spring配置类
- Bean定义
- AOP切面
- 监控配置
- 限流配置
- 分布式ID配置
- 分布式锁配置

## 模块依赖
- 依赖：`common`模块
- 被依赖：`service`、`web`、`infra`模块

## 包结构
```
com.trek0.config/
├── DatabaseConfig.java        # 数据库配置
├── SecurityConfig.java        # 安全配置
├── CacheConfig.java           # 缓存配置
├── AsyncConfig.java           # 异步配置
├── MonitoringConfig.java      # 监控配置
├── RateLimitConfig.java       # 限流配置
├── Resilience4jConfig.java    # 熔断配置
└── properties/                # 配置属性
    └── AppProperties.java
```

## 核心配置
- **DatabaseConfig**: 数据库连接和事务配置
- **SecurityConfig**: Spring Security安全配置
- **CacheConfig**: Redis缓存配置
- **MonitoringConfig**: Micrometer监控配置
- **RateLimitConfig**: Bucket4j限流配置

## 开发规范
- 配置类使用@Configuration注解
- Bean定义使用@Bean注解
- 配置属性使用@ConfigurationProperties
- AOP切面使用@Aspect注解
- 监控指标使用Micrometer
- 配置类应该是无状态的 