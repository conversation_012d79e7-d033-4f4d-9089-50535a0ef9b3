<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC
    "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
    "https://checkstyle.org/dtds/configuration_1_3.dtd">
<module name="Checker">
    <module name="TreeWalker">
        <module name="JavadocMethod"/>
        <module name="JavadocType"/>
        <module name="JavadocVariable"/>
        <module name="JavadocPackage"/>
        <module name="RegexpSinglelineJava"/>
        <module name="FileTabCharacter"/>
        <module name="LineLength">
            <property name="max" value="120"/>
        </module>
        <module name="Indentation">
            <property name="basicOffset" value="4"/>
            <property name="tabWidth" value="4"/>
        </module>
        <module name="WhitespaceAfter"/>
        <module name="WhitespaceAround"/>
        <module name="NoLineWrap"/>
        <module name="NeedBraces"/>
        <module name="EqualsHashCode"/>
        <module name="FinalClass"/>
        <module name="FinalParameters"/>
        <module name="HideUtilityClassConstructor"/>
        <module name="IllegalCatch"/>
        <module name="IllegalThrows"/>
        <module name="InnerTypeLast"/>
        <module name="LeftCurly"/>
        <module name="RightCurly"/>
        <module name="MethodParamPad"/>
        <module name="NoWhitespaceAfter"/>
        <module name="NoWhitespaceBefore"/>
        <module name="OneStatementPerLine"/>
        <module name="OperatorWrap"/>
        <module name="OuterTypeFilename"/>
        <module name="ParameterAssignment"/>
        <module name="RedundantImport"/>
        <module name="RedundantModifier"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StaticVariableName"/>
        <module name="UnnecessaryParentheses"/>
        <module name="VariableDeclarationUsageDistance"/>
    </module>
</module> 