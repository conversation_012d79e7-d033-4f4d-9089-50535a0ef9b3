# Trek0 API 主配置文件
# 整合企业级功能配置

spring:
  profiles:
    # 激活企业级配置文件
    active: dev
    
  application:
    name: trek0-api
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      
# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
    
# 应用自定义配置
trek0:
  # 文件存储
  file:
    upload-dir: ${UPLOAD_DIR:./uploads}
    max-size: 10MB
    
  # JWT配置
  jwt:
    secret: trek0-jwt-secret-key-for-development-environment-must-be-at-least-512-bits-long-for-hs512-algorithm
    expiration: 86400000 # 24小时
    
  # 业务配置
  business:
    user:
      max-footprints-per-user: 1000
    footprint:
      max-title-length: 100
      max-description-length: 1000

# 日志配置
logging:
  level:
    com.trek0.api: DEBUG
    org.springframework.web: INFO
    io.github.resilience4j: DEBUG

# 禁用Zipkin追踪
management:
  tracing:
    enabled: false
  zipkin:
    tracing:
      enabled: false