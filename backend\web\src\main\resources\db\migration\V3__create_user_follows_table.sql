-- 创建用户关注关系表
CREATE TABLE user_follows (
    follower_id BIGINT NOT NULL,
    following_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (follower_id, following_id),
    FOREIGN KEY (follower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (following_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT check_not_self_follow CHECK (follower_id != following_id)
);

-- 创建索引以提高查询性能
CREATE INDEX idx_user_follows_follower ON user_follows(follower_id);
CREATE INDEX idx_user_follows_following ON user_follows(following_id);

-- 添加注释
COMMENT ON TABLE user_follows IS '用户关注关系表';
COMMENT ON COLUMN user_follows.follower_id IS '关注者用户ID';
COMMENT ON COLUMN user_follows.following_id IS '被关注者用户ID';
COMMENT ON COLUMN user_follows.created_at IS '关注时间';