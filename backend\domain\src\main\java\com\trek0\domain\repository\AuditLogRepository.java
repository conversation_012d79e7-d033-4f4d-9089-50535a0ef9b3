package com.trek0.domain.repository;

import com.trek0.domain.model.AuditLog;
import com.trek0.domain.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {
    
    /**
     * 根据表名查找审计日志
     */
    Page<AuditLog> findByTableNameOrderByCreatedAtDesc(String tableName, Pageable pageable);
    
    /**
     * 根据操作类型查找审计日志
     */
    Page<AuditLog> findByOperationTypeOrderByCreatedAtDesc(AuditLog.OperationType operationType, Pageable pageable);
    
    /**
     * 根据用户查找审计日志
     */
    Page<AuditLog> findByUserOrderByCreatedAtDesc(User user, Pageable pageable);
    
    /**
     * 根据记录ID查找审计日志
     */
    List<AuditLog> findByRecordIdOrderByCreatedAtDesc(String recordId);
    
    /**
     * 根据IP地址查找审计日志
     */
    List<AuditLog> findByIpAddressOrderByCreatedAtDesc(String ipAddress);
    
    /**
     * 根据时间范围查找审计日志
     */
    Page<AuditLog> findByCreatedAtBetweenOrderByCreatedAtDesc(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据表名和操作类型查找审计日志
     */
    Page<AuditLog> findByTableNameAndOperationTypeOrderByCreatedAtDesc(String tableName, AuditLog.OperationType operationType, Pageable pageable);
    
    /**
     * 根据用户和时间范围查找审计日志
     */
    Page<AuditLog> findByUserAndCreatedAtBetweenOrderByCreatedAtDesc(User user, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 统计指定时间范围内的操作次数
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.operationType = :operationType AND a.createdAt BETWEEN :startTime AND :endTime")
    long countByOperationTypeAndTimeRange(@Param("operationType") AuditLog.OperationType operationType, 
                                         @Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计用户在指定时间范围内的操作次数
     */
    long countByUserAndCreatedAtBetween(User user, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近的审计日志
     */
    List<AuditLog> findTop10ByOrderByCreatedAtDesc();
    
    /**
     * 根据表名和记录ID查找审计日志
     */
    List<AuditLog> findByTableNameAndRecordIdOrderByCreatedAtDesc(String tableName, String recordId);
    
    /**
     * 删除指定时间之前的审计日志
     */
    void deleteByCreatedAtBefore(LocalDateTime cutoffTime);
}


