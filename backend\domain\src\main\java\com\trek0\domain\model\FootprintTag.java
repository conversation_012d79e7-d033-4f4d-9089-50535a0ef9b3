package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

@Entity
@Table(name = "footprint_tags")
public class FootprintTag {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "tag_id")
    private Long tagId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "footprint_id", nullable = false)
    @JsonBackReference
    private Footprint footprint;
    
    @NotBlank(message = "标签名称不能为空")
    @Size(min = 1, max = 50, message = "标签名称长度必须在1-50个字符之间")
    @Column(name = "tag_name", nullable = false, length = 50)
    private String tagName;
    
    @Size(max = 30, message = "标签分类不能超过30个字符")
    @Column(name = "tag_category", length = 30)
    private String tagCategory;
    
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "标签颜色必须是有效的十六进制颜色代码")
    @Column(name = "tag_color", length = 7)
    private String tagColor;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    // Constructors
    public FootprintTag() {}
    
    public FootprintTag(Footprint footprint, String tagName) {
        this.footprint = footprint;
        this.tagName = tagName;
    }
    
    public FootprintTag(Footprint footprint, String tagName, String tagCategory) {
        this.footprint = footprint;
        this.tagName = tagName;
        this.tagCategory = tagCategory;
    }
    
    public FootprintTag(Footprint footprint, String tagName, String tagCategory, String tagColor) {
        this.footprint = footprint;
        this.tagName = tagName;
        this.tagCategory = tagCategory;
        this.tagColor = tagColor;
    }
    
    // Getters and Setters
    public Long getTagId() {
        return tagId;
    }
    
    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }
    
    public Footprint getFootprint() {
        return footprint;
    }
    
    public void setFootprint(Footprint footprint) {
        this.footprint = footprint;
    }
    
    public String getTagName() {
        return tagName;
    }
    
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }
    
    public String getTagCategory() {
        return tagCategory;
    }
    
    public void setTagCategory(String tagCategory) {
        this.tagCategory = tagCategory;
    }
    
    public String getTagColor() {
        return tagColor;
    }
    
    public void setTagColor(String tagColor) {
        this.tagColor = tagColor;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    // 向后兼容性方法
    public Long getId() {
        return tagId;
    }
    
    public void setId(Long id) {
        this.tagId = id;
    }
    
    public String getName() {
        return tagName;
    }
    
    public void setName(String name) {
        this.tagName = name;
    }
    
    public String getCategory() {
        return tagCategory;
    }
    
    public void setCategory(String category) {
        this.tagCategory = category;
    }
    
    public String getColor() {
        return tagColor;
    }
    
    public void setColor(String color) {
        this.tagColor = color;
    }
    
    // 业务方法
    public boolean hasColor() {
        return tagColor != null && !tagColor.trim().isEmpty();
    }
    
    public boolean hasCategory() {
        return tagCategory != null && !tagCategory.trim().isEmpty();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof FootprintTag)) return false;
        FootprintTag that = (FootprintTag) o;
        return tagId != null && tagId.equals(that.tagId);
    }
    
    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
    
    @Override
    public String toString() {
        return "FootprintTag{" +
                "tagId=" + tagId +
                ", tagName='" + tagName + '\'' +
                ", tagCategory='" + tagCategory + '\'' +
                ", tagColor='" + tagColor + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}

