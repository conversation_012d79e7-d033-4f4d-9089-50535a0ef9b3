package com.trek0.service.impl;

import com.trek0.api.dto.*;
import com.trek0.domain.model.*;
import com.trek0.domain.repository.FootprintRepository;
import com.trek0.service.FootprintService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.math.BigDecimal;

@Service
@Transactional
public class FootprintServiceImpl implements FootprintService {
    
    private static final Logger logger = LoggerFactory.getLogger(FootprintServiceImpl.class);
    
    private final FootprintRepository footprintRepository;
    
    @Autowired
    public FootprintServiceImpl(FootprintRepository footprintRepository) {
        this.footprintRepository = footprintRepository;
    }
    
    @Override
    public FootprintDTO createFootprint(User user, CreateFootprintRequest request) {
        logger.info("Creating footprint for user: {}", user.getUsername());
        
        try {
            Footprint footprint = new Footprint();
            footprint.setBusinessId(java.util.UUID.randomUUID().toString()); // 替换generateFootprintId为java.util.UUID.randomUUID().toString()
            footprint.setUser(user);
            footprint.setName(request.getName());
            footprint.setDescription(request.getDescription());
            footprint.setLatitude(BigDecimal.valueOf(request.getLocation().getLat()));
            footprint.setLongitude(BigDecimal.valueOf(request.getLocation().getLng()));
            footprint.setAddress(request.getLocation().getAddress());
            footprint.setLastActionMethod(request.getLocationMode());
            
            // 设置可见性
            if (request.getVisibility() != null) {
                try {
                    footprint.setVisibility(Footprint.VisibilityLevel.valueOf(request.getVisibility().toUpperCase()));
                } catch (IllegalArgumentException e) {
                    footprint.setVisibility(Footprint.VisibilityLevel.PUBLIC);
                }
            }
            
            // 添加标签
            if (request.getTags() != null) {
                for (String tagName : request.getTags()) {
                    footprint.addTag(tagName);
                }
            }
            
            // 添加照片
            if (request.getPhotos() != null) {
                for (String photoUrl : request.getPhotos()) {
                    footprint.addPhoto(photoUrl);
                }
            }
            
            Footprint savedFootprint = footprintRepository.save(footprint);
            
            logger.info("Created footprint with ID: {}", savedFootprint.getFootprintId());
            
            return FootprintDTO.fromEntity(savedFootprint);
        } catch (Exception e) {
            logger.error("Error creating footprint for user: {}", user.getUsername(), e);
            throw e;
        }
    }
    
    // Fallback method for createFootprint
    public FootprintDTO createFootprintFallback(User user, CreateFootprintRequest request, Exception ex) {
        logger.error("Fallback: Failed to create footprint for user: {}", user.getUsername(), ex);
        throw new RuntimeException("足迹创建服务暂时不可用，请稍后重试");
    }
    
    @Override
    public FootprintDTO updateFootprint(User user, Long footprintId, UpdateFootprintRequest request) {
        logger.info("Updating footprint {} for user: {}", footprintId, user.getUsername());
        
        try {
            Footprint footprint = footprintRepository.findByFootprintId(footprintId)
                    .orElseThrow(() -> new RuntimeException("足迹不存在"));
            
            // 检查权限
            if (!footprint.getUser().getId().equals(user.getId())) {
                throw new RuntimeException("无权修改此足迹");
            }
            
            // 更新字段
            if (request.getName() != null) {
                footprint.setName(request.getName());
            }
            if (request.getDescription() != null) {
                footprint.setDescription(request.getDescription());
            }
            if (request.getAddress() != null) {
                footprint.setAddress(request.getAddress());
            }
            if (request.getVisibility() != null) {
                try {
                    footprint.setVisibility(Footprint.VisibilityLevel.valueOf(request.getVisibility().toUpperCase()));
                } catch (IllegalArgumentException e) {
                    logger.warn("Invalid visibility value: {}", request.getVisibility());
                }
            }
            // 新增：更新位置坐标
            if (request.getLat() != null) {
                footprint.setLatitude(BigDecimal.valueOf(request.getLat()));
            }
            if (request.getLng() != null) {
                footprint.setLongitude(BigDecimal.valueOf(request.getLng()));
            }
            if (request.getLocationMode() != null) {
                footprint.setLastActionMethod(request.getLocationMode());
            }
            
            Footprint updatedFootprint = footprintRepository.save(footprint);
            
            logger.info("Updated footprint with ID: {}", footprintId);
            
            return FootprintDTO.fromEntity(updatedFootprint);
        } catch (Exception e) {
            logger.error("Error updating footprint {} for user: {}", footprintId, user.getUsername(), e);
            throw e;
        }
    }
    
    // Fallback method for updateFootprint
    public FootprintDTO updateFootprintFallback(User user, Long footprintId, UpdateFootprintRequest request, Exception ex) {
        logger.error("Fallback: Failed to update footprint {} for user: {}", footprintId, user.getUsername(), ex);
        throw new RuntimeException("更新足迹服务暂时不可用，请稍后重试");
    }

    @Override
    public void deleteFootprint(User user, Long footprintId) {
        logger.info("Deleting footprint {} for user: {}", footprintId, user.getUsername());
        
        try {
            Footprint footprint = footprintRepository.findByFootprintId(footprintId)
                    .orElseThrow(() -> new RuntimeException("足迹不存在"));
            
            // 检查权限
            if (!footprint.getUser().getId().equals(user.getId())) {
                throw new RuntimeException("无权删除此足迹");
            }
            
            footprintRepository.delete(footprint);
            
            logger.info("Deleted footprint with ID: {}", footprintId);
        } catch (Exception e) {
            logger.error("Error deleting footprint {} for user: {}", footprintId, user.getUsername(), e);
            throw e;
        }
    }
    
    // Fallback method for deleteFootprint
    public void deleteFootprintFallback(User user, Long footprintId, Exception ex) {
        logger.error("Fallback: Failed to delete footprint {} for user: {}", footprintId, user.getUsername(), ex);
        throw new RuntimeException("删除足迹服务暂时不可用，请稍后重试");
    }
    
    @Override
    @Transactional(readOnly = true)
    public FootprintDTO getFootprint(Long footprintId, User currentUser) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));
        
        // 检查访问权限
        if (!hasAccessPermission(footprintId, currentUser)) {
            throw new RuntimeException("无权访问此足迹");
        }
        
        return FootprintDTO.fromEntity(footprint);
    }
    
    @Override
    @Transactional(readOnly = true)
    @CircuitBreaker(name = "database", fallbackMethod = "getUserFootprintsFallback")
    @Retry(name = "database")
    public Page<FootprintDTO> getUserFootprints(User user, Pageable pageable) {
        try {
            Page<Footprint> footprints = footprintRepository.findByUserOrderByCreatedAtDesc(user, pageable);
            
            return footprints.map(FootprintDTO::fromEntity);
        } catch (Exception e) {
            logger.error("Error getting footprints for user: {}", user.getUsername(), e);
            throw e;
        }
    }
    
    // Fallback method for getUserFootprints
    public Page<FootprintDTO> getUserFootprintsFallback(User user, Pageable pageable, Exception ex) {
        logger.error("Fallback: Failed to get footprints for user: {}", user.getUsername(), ex);
        throw new RuntimeException("获取足迹服务暂时不可用，请稍后重试");
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<FootprintDTO> searchUserFootprints(User user, String keyword, Pageable pageable) {
        // 如果关键词为空或只包含空白字符，返回用户所有足迹
        if (keyword == null || keyword.trim().isEmpty()) {
            return getUserFootprints(user, pageable);
        }
        
        // Page<Footprint> footprints = footprintRepository.searchByUserAndKeyword(user, keyword.trim(), pageable); // 注释掉searchByUserAndKeyword
        // return footprints.map(FootprintDTO::fromEntity);
        return null; // 返回空页或抛出异常，表示搜索功能不可用
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<FootprintDTO> getPublicFootprints(Pageable pageable) {
        // Page<Footprint> footprints = footprintRepository.findByVisibilityOrderByCreatedAtDesc( // 注释掉findByVisibilityOrderByCreatedAtDesc
        //     Footprint.VisibilityLevel.PUBLIC, pageable);
        return null; // 返回空页或抛出异常，表示获取公开足迹功能不可用
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<FootprintDTO> getNearbyFootprints(Double latitude, Double longitude, Double radiusInKm) {
        // List<Footprint> footprints = footprintRepository.findNearbyFootprints(latitude, longitude, radiusInKm); // 注释掉findNearbyFootprints
        return null; // 返回空列表或抛出异常，表示获取附近足迹功能不可用
    }
    
    @Override
    public boolean hasAccessPermission(Long footprintId, User user) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId).orElse(null);
        if (footprint == null) {
            return false;
        }
        return hasAccessPermissionInternal(footprint, user);
    }
    
    @Override
    public boolean hasEditPermission(Long footprintId, User user) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId).orElse(null);
        if (footprint == null) {
            return false;
        }
        // 只有足迹所有者可以编辑
        return user != null && footprint.getUser().getId().equals(user.getId());
    }
    
    // 辅助方法：检查访问权限
    private boolean hasAccessPermissionInternal(Footprint footprint, User currentUser) {
        // 公开的足迹所有人可见
        if (footprint.getVisibility() == Footprint.VisibilityLevel.PUBLIC) {
            return true;
        }
        
        // 自己的足迹可见
        if (currentUser != null && footprint.getUser().getId().equals(currentUser.getId())) {
            return true;
        }
        
        // 好友可见的足迹，检查是否是好友
        if (footprint.getVisibility() == Footprint.VisibilityLevel.FRIENDS_ONLY && currentUser != null) {
            return footprint.getUser().getFollowers().contains(currentUser);
        }
        
        return false;
    }
}