package com.trek0.web;

import com.trek0.domain.model.User;
import com.trek0.service.UserService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import java.util.Optional;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest
public class UserControllerTest {
    @Autowired
    private MockMvc mockMvc;
    @MockBean
    private UserService userService;

    @Test
    void getUserById_found() throws Exception {
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        Mockito.when(userService.findUserById(1L)).thenReturn(Optional.of(user));
        mockMvc.perform(get("/api/v1/users/1"))
                .andExpect(status().isOk());
    }

    @Test
    void getUserById_notFound() throws Exception {
        Mockito.when(userService.findUserById(anyLong())).thenReturn(Optional.empty());
        mockMvc.perform(get("/api/v1/users/999"))
                .andExpect(status().isNotFound());
    }
} 