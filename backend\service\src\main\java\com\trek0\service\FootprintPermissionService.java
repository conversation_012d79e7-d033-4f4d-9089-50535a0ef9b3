package com.trek0.service;

import com.trek0.domain.model.User;
import com.trek0.domain.model.Footprint;

/**
 * 足迹权限管理服务
 */
public interface FootprintPermissionService {
    
    /**
     * 检查用户是否有足迹访问权限
     */
    boolean hasAccessPermission(Footprint footprint, User user);
    
    /**
     * 检查用户是否有足迹修改权限
     */
    boolean hasEditPermission(Footprint footprint, User user);
    
    /**
     * 检查用户是否有足迹删除权限
     */
    boolean hasDeletePermission(Footprint footprint, User user);
    
    /**
     * 检查用户是否可以查看足迹的私有信息
     */
    boolean canViewPrivateInfo(Footprint footprint, User user);
    
    /**
     * 验证足迹访问权限，无权限时抛出异常
     */
    void validateAccessPermission(Footprint footprint, User user);
    
    /**
     * 验证足迹修改权限，无权限时抛出异常
     */
    void validateEditPermission(Footprint footprint, User user);
}