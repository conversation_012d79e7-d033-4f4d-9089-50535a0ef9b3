package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 足迹DTO
 */
@Schema(description = "足迹信息")
public class FootprintDTO {
    
    @Schema(description = "足迹ID")
    private Long id;
    
    @Schema(description = "足迹业务ID")
    private String footprintId;
    
    @NotBlank(message = "足迹名称不能为空")
    @Size(max = 100, message = "足迹名称长度不能超过100个字符")
    @Schema(description = "足迹名称", required = true)
    private String name;
    
    @Size(max = 1000, message = "足迹描述长度不能超过1000个字符")
    @Schema(description = "足迹描述")
    private String description;
    
    @NotNull(message = "位置信息不能为空")
    @Schema(description = "位置信息", required = true)
    private LocationDTO location;
    
    @Schema(description = "标签列表")
    private Set<String> tags;
    
    @Schema(description = "照片列表")
    private List<String> photos;
    
    @Schema(description = "可见性")
    private String visibility;
    
    @Schema(description = "路径列表")
    private List<FootprintPathDTO> paths;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "用户信息")
    private UserSummaryDTO user;
    
    @Schema(description = "最后操作方法")
    private String lastActionMethod;

    public FootprintDTO() {}
    // 可根据需要补全全参构造
    // getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getFootprintId() { return footprintId; }
    public void setFootprintId(String footprintId) { this.footprintId = footprintId; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public LocationDTO getLocation() { return location; }
    public void setLocation(LocationDTO location) { this.location = location; }
    public Set<String> getTags() { return tags; }
    public void setTags(Set<String> tags) { this.tags = tags; }
    public List<String> getPhotos() { return photos; }
    public void setPhotos(List<String> photos) { this.photos = photos; }
    public String getVisibility() { return visibility; }
    public void setVisibility(String visibility) { this.visibility = visibility; }
    public List<FootprintPathDTO> getPaths() { return paths; }
    public void setPaths(List<FootprintPathDTO> paths) { this.paths = paths; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public UserSummaryDTO getUser() { return user; }
    public void setUser(UserSummaryDTO user) { this.user = user; }
    public String getLastActionMethod() { return lastActionMethod; }
    public void setLastActionMethod(String lastActionMethod) { this.lastActionMethod = lastActionMethod; }
    @Override
    public String toString() {
        return "FootprintDTO{" +
                "id=" + id +
                ", footprintId='" + footprintId + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", location=" + location +
                ", tags=" + tags +
                ", photos=" + photos +
                ", visibility='" + visibility + '\'' +
                ", paths=" + paths +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", user=" + user +
                ", lastActionMethod='" + lastActionMethod + '\'' +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FootprintDTO that = (FootprintDTO) o;
        return java.util.Objects.equals(id, that.id) &&
                java.util.Objects.equals(footprintId, that.footprintId) &&
                java.util.Objects.equals(name, that.name) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(location, that.location) &&
                java.util.Objects.equals(tags, that.tags) &&
                java.util.Objects.equals(photos, that.photos) &&
                java.util.Objects.equals(visibility, that.visibility) &&
                java.util.Objects.equals(paths, that.paths) &&
                java.util.Objects.equals(createdAt, that.createdAt) &&
                java.util.Objects.equals(updatedAt, that.updatedAt) &&
                java.util.Objects.equals(user, that.user) &&
                java.util.Objects.equals(lastActionMethod, that.lastActionMethod);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(id, footprintId, name, description, location, tags, photos, visibility, paths, createdAt, updatedAt, user, lastActionMethod);
    }
    // 内部类 LocationDTO
    /**
     * 位置信息内部类
     */
    @Schema(description = "位置信息")
    public static class LocationDTO {
        @NotNull(message = "纬度不能为空")
        @Schema(description = "纬度", required = true)
        private Double lat;
        @NotNull(message = "经度不能为空")
        @Schema(description = "经度", required = true)
        private Double lng;
        @Size(max = 200, message = "地址长度不能超过200个字符")
        @Schema(description = "地址")
        private String address;
        public LocationDTO() {}
        public LocationDTO(Double lat, Double lng, String address) {
            this.lat = lat;
            this.lng = lng;
            this.address = address;
        }
        public Double getLat() { return lat; }
        public void setLat(Double lat) { this.lat = lat; }
        public Double getLng() { return lng; }
        public void setLng(Double lng) { this.lng = lng; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
        @Override
        public String toString() {
            return "LocationDTO{" +
                    "lat=" + lat +
                    ", lng=" + lng +
                    ", address='" + address + '\'' +
                    '}';
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            LocationDTO that = (LocationDTO) o;
            return java.util.Objects.equals(lat, that.lat) &&
                    java.util.Objects.equals(lng, that.lng) &&
                    java.util.Objects.equals(address, that.address);
        }
        @Override
        public int hashCode() {
            return java.util.Objects.hash(lat, lng, address);
        }
    }
    /**
     * 用户摘要信息内部类
     */
    @Schema(description = "用户摘要信息")
    public static class UserSummaryDTO {
        @Schema(description = "用户ID")
        private Long id;
        @Schema(description = "用户名")
        private String username;
        @Schema(description = "头像")
        private String profileImage;
        public UserSummaryDTO() {}
        public UserSummaryDTO(Long id, String username, String profileImage) {
            this.id = id;
            this.username = username;
            this.profileImage = profileImage;
        }
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getProfileImage() { return profileImage; }
        public void setProfileImage(String profileImage) { this.profileImage = profileImage; }
        @Override
        public String toString() {
            return "UserSummaryDTO{" +
                    "id=" + id +
                    ", username='" + username + '\'' +
                    ", profileImage='" + profileImage + '\'' +
                    '}';
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            UserSummaryDTO that = (UserSummaryDTO) o;
            return java.util.Objects.equals(id, that.id) &&
                    java.util.Objects.equals(username, that.username) &&
                    java.util.Objects.equals(profileImage, that.profileImage);
        }
        @Override
        public int hashCode() {
            return java.util.Objects.hash(id, username, profileImage);
        }
    }

    public static FootprintDTO fromEntity(com.trek0.domain.model.Footprint entity) {
        if (entity == null) return null;
        FootprintDTO dto = new FootprintDTO();
        dto.setId(entity.getId());
        dto.setFootprintId(entity.getBusinessId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setVisibility(entity.getVisibilityLevel() != null ? entity.getVisibilityLevel().name() : null);
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        // 可补充tags/photos/paths/user等映射
        return dto;
    }
}