-- 企业标准足迹表 - 重构版本

CREATE TABLE footprints (
    footprint_id BIGSERIAL PRIMARY KEY,
    business_id VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    footprint_name VARCHAR(200) NOT NULL,
    footprint_description TEXT,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address_text VARCHAR(500),
    visibility_level VARCHAR(20) NOT NULL DEFAULT 'PUBLIC',
    last_action_method VARCHAR(50),
    footprint_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    view_count INTEGER NOT NULL DEFAULT 0,
    like_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_footprint_user FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_latitude_range CHECK (latitude >= -90 AND latitude <= 90),
    CONSTRAINT chk_longitude_range CHECK (longitude >= -180 AND longitude <= 180),
    CONSTRAINT chk_visibility_level CHECK (visibility_level IN ('PUBLIC', 'PRIVATE', 'FRIENDS_ONLY', 'UNLISTED')),
    CONSTRAINT chk_footprint_status CHECK (footprint_status IN ('ACTIVE', 'ARCHIVED', 'DELETED', 'DRAFT')),
    CONSTRAINT chk_footprint_name_length CHECK (LENGTH(footprint_name) >= 1)
);

-- 企业级索引策略
CREATE INDEX idx_footprints_user_id ON footprints(user_id, created_at DESC);
CREATE INDEX idx_footprints_business_id ON footprints(business_id);
CREATE INDEX idx_footprints_location ON footprints(latitude, longitude);
CREATE INDEX idx_footprints_visibility ON footprints(visibility_level, footprint_status);
CREATE INDEX idx_footprints_created_at ON footprints(created_at DESC);
CREATE INDEX idx_footprints_status ON footprints(footprint_status);
CREATE INDEX idx_footprints_view_count ON footprints(view_count DESC);

-- 地理空间索引（如果需要）
-- CREATE INDEX idx_footprints_geo ON footprints USING GIST (ST_Point(longitude, latitude));

-- 企业级注释
COMMENT ON TABLE footprints IS '企业标准足迹表 - 地理位置记录';
COMMENT ON COLUMN footprints.footprint_id IS '足迹主键ID';
COMMENT ON COLUMN footprints.business_id IS '足迹业务唯一标识符';
COMMENT ON COLUMN footprints.user_id IS '创建用户ID（外键）';
COMMENT ON COLUMN footprints.footprint_name IS '足迹名称';
COMMENT ON COLUMN footprints.footprint_description IS '足迹描述';
COMMENT ON COLUMN footprints.latitude IS '纬度（-90到90）';
COMMENT ON COLUMN footprints.longitude IS '经度（-180到180）';
COMMENT ON COLUMN footprints.address_text IS '地址文本描述';
COMMENT ON COLUMN footprints.visibility_level IS '可见性级别';
COMMENT ON COLUMN footprints.last_action_method IS '最后操作方法';
COMMENT ON COLUMN footprints.footprint_status IS '足迹状态';
COMMENT ON COLUMN footprints.view_count IS '查看次数';
COMMENT ON COLUMN footprints.like_count IS '点赞次数';
COMMENT ON COLUMN footprints.created_at IS '创建时间';
COMMENT ON COLUMN footprints.updated_at IS '更新时间';
COMMENT ON COLUMN footprints.deleted_at IS '删除时间（软删除）';