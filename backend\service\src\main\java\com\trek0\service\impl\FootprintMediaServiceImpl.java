package com.trek0.service.impl;

import com.trek0.domain.model.User;
import com.trek0.domain.model.Footprint;
import com.trek0.domain.repository.FootprintRepository;
import com.trek0.api.dto.FootprintDTO;
import com.trek0.service.FootprintMediaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class FootprintMediaServiceImpl implements FootprintMediaService {
    
    private static final Logger logger = LoggerFactory.getLogger(FootprintMediaServiceImpl.class);
    
    private final FootprintRepository footprintRepository;
    
    @Autowired
    public FootprintMediaServiceImpl(FootprintRepository footprintRepository) {
        this.footprintRepository = footprintRepository;
    }
    
    public FootprintDTO addTag(User user, Long footprintId, String tag) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));
        
        // 检查权限
        if (!footprint.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权修改此足迹");
        }
        
        footprint.addTag(tag);
        footprint = footprintRepository.save(footprint);
        
        logger.info("Added tag '{}' to footprint {}", tag, footprint.getFootprintId());
        return FootprintDTO.fromEntity(footprint);
    }
    
    public FootprintDTO removeTag(User user, Long footprintId, String tag) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));
        
        // 检查权限
        if (!footprint.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权修改此足迹");
        }
        
        footprint.removeTagByName(tag);
        footprint = footprintRepository.save(footprint);
        
        logger.info("Removed tag '{}' from footprint {}", tag, footprint.getFootprintId());
        return FootprintDTO.fromEntity(footprint);
    }
    
    public List<String> getFootprintTags(Long footprintId) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));

        return footprint.getTags() != null ? 
            footprint.getTags().stream()
                .map(tag -> tag.getName())
                .collect(java.util.stream.Collectors.toList()) : 
            new java.util.ArrayList<>();
    }
    
    public FootprintDTO addPhoto(User user, Long footprintId, String photoUrl) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));
        
        // 检查权限
        if (!footprint.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权修改此足迹");
        }
        
        footprint.addPhoto(photoUrl);
        footprint = footprintRepository.save(footprint);
        
        logger.info("Added photo to footprint {}", footprint.getFootprintId());
        return FootprintDTO.fromEntity(footprint);
    }
    
    public FootprintDTO removePhoto(User user, Long footprintId, String photoUrl) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));
        
        // 检查权限
        if (!footprint.getUser().getId().equals(user.getId())) {
            throw new RuntimeException("无权修改此足迹");
        }
        
        // 查找并删除匹配的照片
        footprint.getPhotos().removeIf(photo -> photo.getImageUrl().equals(photoUrl));
        footprint = footprintRepository.save(footprint);
        
        logger.info("Removed photo from footprint {}", footprint.getFootprintId());
        return FootprintDTO.fromEntity(footprint);
    }
    
    public List<String> getFootprintPhotos(Long footprintId) {
        Footprint footprint = footprintRepository.findByFootprintId(footprintId)
            .orElseThrow(() -> new RuntimeException("足迹不存在"));

        return footprint.getPhotos() != null ? 
            footprint.getPhotos().stream()
                .map(photo -> photo.getImageUrl())
                .collect(java.util.stream.Collectors.toList()) : 
            new java.util.ArrayList<>();
    }
}