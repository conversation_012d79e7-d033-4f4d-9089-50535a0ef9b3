-- 企业标准路径点表 - 重构版本

CREATE TABLE path_points (
    point_id BIGSERIAL PRIMARY KEY,
    path_id BIGINT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    elevation_meters DECIMAL(8, 2),
    point_sequence INTEGER NOT NULL,
    recorded_at TIMESTAMP NOT NULL,
    point_type VARCHAR(20) NOT NULL DEFAULT 'TRACK',
    speed_kmh DECIMAL(6, 2),
    bearing_degrees DECIMAL(5, 2),
    accuracy_meters DECIMAL(6, 2),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_point_path FOREIGN KEY (path_id) REFERENCES footprint_paths(path_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_point_latitude CHECK (latitude >= -90 AND latitude <= 90),
    CONSTRAINT chk_point_longitude CHECK (longitude >= -180 AND longitude <= 180),
    CONSTRAINT chk_point_sequence CHECK (point_sequence >= 0),
    CONSTRAINT chk_point_type CHECK (point_type IN ('START', 'TRACK', 'WAYPOINT', 'END', 'PAUSE', 'RESUME')),
    CONSTRAINT chk_speed CHECK (speed_kmh IS NULL OR speed_kmh >= 0),
    CONSTRAINT chk_bearing CHECK (bearing_degrees IS NULL OR (bearing_degrees >= 0 AND bearing_degrees < 360)),
    CONSTRAINT chk_accuracy CHECK (accuracy_meters IS NULL OR accuracy_meters >= 0),
    CONSTRAINT uk_path_sequence UNIQUE (path_id, point_sequence)
);

-- 企业级索引策略
CREATE INDEX idx_path_points_path_id ON path_points(path_id, point_sequence);
CREATE INDEX idx_path_points_location ON path_points(latitude, longitude);
CREATE INDEX idx_path_points_recorded_at ON path_points(recorded_at);
CREATE INDEX idx_path_points_type ON path_points(point_type);

-- 地理空间索引（如果需要）
-- CREATE INDEX idx_path_points_geo ON path_points USING GIST (ST_Point(longitude, latitude));

-- 企业级注释
COMMENT ON TABLE path_points IS '企业标准路径点表 - GPS轨迹数据';
COMMENT ON COLUMN path_points.point_id IS '路径点主键ID';
COMMENT ON COLUMN path_points.path_id IS '路径ID（外键）';
COMMENT ON COLUMN path_points.latitude IS '纬度（-90到90）';
COMMENT ON COLUMN path_points.longitude IS '经度（-180到180）';
COMMENT ON COLUMN path_points.elevation_meters IS '海拔高度（米）';
COMMENT ON COLUMN path_points.point_sequence IS '点在路径中的序号';
COMMENT ON COLUMN path_points.recorded_at IS 'GPS记录时间';
COMMENT ON COLUMN path_points.point_type IS '点类型：START-起点, TRACK-轨迹点, WAYPOINT-路标点, END-终点';
COMMENT ON COLUMN path_points.speed_kmh IS '当时速度（公里/小时）';
COMMENT ON COLUMN path_points.bearing_degrees IS '方向角度（0-359度）';
COMMENT ON COLUMN path_points.accuracy_meters IS 'GPS精度（米）';
COMMENT ON COLUMN path_points.created_at IS '数据创建时间';