package com.trek0.web.controller;

import com.trek0.api.dto.UserDTO;
import com.trek0.service.UserService;
import com.trek0.service.VerificationService;
import com.trek0.service.UserLoginRecordService;
import com.trek0.web.security.JwtTokenProvider;
import com.trek0.common.metrics.BusinessMetrics;
import com.trek0.common.util.T0IdGenerator;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Timer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import jakarta.servlet.http.HttpServletRequest;
import java.util.*;
import com.trek0.domain.model.User;

/**
 * 用户认证控制器
 * 
 * <p>提供用户登录、注册、密码重置等认证相关功能。</p>
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关API")
public class AuthController {
    
    private static final Logger log = LoggerFactory.getLogger(AuthController.class);
    
    private final UserService userService;
    private final VerificationService verificationService;
    private final UserLoginRecordService userLoginRecordService;
    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;
    private final PasswordEncoder passwordEncoder;
    private final BusinessMetrics businessMetrics;
    
    public AuthController(UserService userService, VerificationService verificationService, 
                         UserLoginRecordService userLoginRecordService, AuthenticationManager authenticationManager,
                         JwtTokenProvider tokenProvider, PasswordEncoder passwordEncoder, BusinessMetrics businessMetrics) {
        this.userService = userService;
        this.verificationService = verificationService;
        this.userLoginRecordService = userLoginRecordService;
        this.authenticationManager = authenticationManager;
        this.tokenProvider = tokenProvider;
        this.passwordEncoder = passwordEncoder;
        this.businessMetrics = businessMetrics;
    }
    private final T0IdGenerator idGenerator = new T0IdGenerator();

    @Value("${recaptcha.secret}")
    private String recaptchaSecret;
    @Value("${recaptcha.url}")
    private String recaptchaUrl;
    @Value("${hcaptcha.sitekey}")
    private String hcaptchaSiteKey;

    private UserDTO toDTO(User user) {
        if (user == null) return null;
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setEmail(user.getEmail());
        dto.setUsername(user.getUsername());
        dto.setPassword(user.getPassword());
        dto.setUuid(user.getUuid());
        dto.setIpLocation(user.getIpLocation());
        dto.setProfileImage(user.getProfileImageUrl() != null ? user.getProfileImageUrl() : user.getProfileImage());
        dto.setSignature(user.getUserSignature());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
    private User toEntity(UserDTO dto) {
        if (dto == null) return null;
        User user = new User();
        user.setId(dto.getId());
        user.setEmail(dto.getEmail());
        user.setUsername(dto.getUsername());
        user.setPassword(dto.getPassword());
        user.setUuid(dto.getUuid());
        user.setIpLocation(dto.getIpLocation());
        user.setProfileImageUrl(dto.getProfileImage());
        user.setUserSignature(dto.getSignature());
        user.setCreatedAt(dto.getCreatedAt());
        user.setUpdatedAt(dto.getUpdatedAt());
        return user;
    }

    /**
     * 临时调试方法 - 创建测试用户
     */
    @PostMapping("/debug/create-user")
    public ResponseEntity<?> debugCreateUser(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        String username = request.get("username");
        
        if (email == null || password == null) {
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱和密码不能为空"));
        }
        
        // 检查用户是否已存在
        if (userService.findUserByEmail(email).isPresent()) {
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "用户已存在"));
        }
        
        // 创建新用户
        User user = new User();
        user.setEmail(email);
        user.setUsername(username != null ? username : email);
        user.setPassword(passwordEncoder.encode(password));
        user.setUuid(java.util.UUID.randomUUID().toString()); // 添加UUID
        
        User savedUser = userService.saveUser(user);
        UserDTO savedUserDTO = toDTO(savedUser);
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "用户创建成功");
        response.put("userId", savedUserDTO.getId());
        response.put("email", savedUserDTO.getEmail());
        response.put("username", savedUserDTO.getUsername());
        response.put("uuid", savedUserDTO.getUuid());
        response.put("passwordHash", savedUserDTO.getPassword());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 临时调试方法 - 列出所有用户
     */
    @GetMapping("/debug/list-users")
    public ResponseEntity<?> debugListUsers() {
        List<User> users = userService.findAllUsers();
        List<Map<String, Object>> userList = new ArrayList<>();
        
        for (User user : users) {
            UserDTO userDTO = toDTO(user);
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", userDTO.getId());
            userInfo.put("email", userDTO.getEmail());
            userInfo.put("username", userDTO.getUsername());
            userInfo.put("passwordLength", userDTO.getPassword() != null ? userDTO.getPassword().length() : 0);
            userInfo.put("isBCryptHash", userDTO.getPassword() != null && userDTO.getPassword().startsWith("$2"));
            userList.add(userInfo);
        }
        
        return ResponseEntity.ok(userList);
    }

    /**
     * 临时调试方法 - 检查用户密码
     */
    @GetMapping("/debug/check-password/{email}")
    public ResponseEntity<?> debugCheckPassword(@PathVariable String email) {
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        User user = userOpt.get();
        UserDTO userDTO = toDTO(user);
        Map<String, Object> response = new HashMap<>();
        response.put("email", userDTO.getEmail());
        response.put("passwordHash", userDTO.getPassword());
        response.put("passwordLength", userDTO.getPassword().length());
        response.put("isBCryptHash", userDTO.getPassword().startsWith("$2"));
        
        return ResponseEntity.ok(response);
    }

    /**
     * 临时调试方法 - 测试密码匹配
     */
    @PostMapping("/debug/test-password")
    public ResponseEntity<?> debugTestPassword(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        User user = userOpt.get();
        boolean matches = passwordEncoder.matches(password, user.getPassword());
        
        Map<String, Object> response = new HashMap<>();
        response.put("email", email);
        response.put("passwordMatches", matches);
        response.put("storedPasswordHash", user.getPassword());
        response.put("inputPassword", password);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 临时调试方法 - 直接访问数据库（绕过熔断器）
     */
    @GetMapping("/debug/direct-db-users")
    public ResponseEntity<?> debugDirectDbUsers() {
        try {
            List<User> users = userService.findAllUsers();
            List<Map<String, Object>> userList = new ArrayList<>();
            
            for (User user : users) {
                UserDTO userDTO = toDTO(user);
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", userDTO.getId());
                userInfo.put("email", userDTO.getEmail());
                userInfo.put("username", userDTO.getUsername());
                userInfo.put("uuid", userDTO.getUuid());
                userInfo.put("createdAt", userDTO.getCreatedAt());
                userInfo.put("passwordLength", userDTO.getPassword() != null ? userDTO.getPassword().length() : 0);
                userInfo.put("isBCryptHash", userDTO.getPassword() != null && userDTO.getPassword().startsWith("$2"));
                userList.add(userInfo);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "直接数据库查询成功");
            response.put("userCount", userList.size());
            response.put("users", userList);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "数据库查询失败");
            errorResponse.put("message", e.getMessage());
            errorResponse.put("type", e.getClass().getSimpleName());
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 用户登录
     * 
     * <p>验证用户邮箱和密码，登录成功后返回JWT令牌和用户信息。</p>
     * 
     * @param loginRequest 登录请求信息
     * @param request HTTP请求对象
     * @return 登录成功返回JWT令牌和用户信息，失败返回401状态码
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "通过邮箱和密码进行用户登录")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登录成功"),
        @ApiResponse(responseCode = "400", description = "参数错误"),
        @ApiResponse(responseCode = "401", description = "认证失败")
    })
    public ResponseEntity<?> login(
            @Parameter(description = "登录请求信息", required = true)
            @RequestBody Map<String, String> loginRequest, 
            HttpServletRequest request) {
        
        Timer.Sample timer = (Timer.Sample) businessMetrics.startUserAuthTimer();
        
        try {
            String email = loginRequest.get("email");
            String password = loginRequest.get("password");
            
            log.info("登录尝试 - 邮箱: {}, 密码长度: {}", email, password != null ? password.length() : 0);
            
            if (!StringUtils.hasText(email) || !StringUtils.hasText(password)) {
                businessMetrics.incrementApiError("invalid_parameters");
                return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱和密码不能为空"));
            }

            log.info("开始认证 - 邮箱: {}", email);
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(email, password)
            );
            log.info("认证成功 - 邮箱: {}", email);

            SecurityContextHolder.getContext().setAuthentication(authentication);
            User user = userService.findUserByEmail(email).orElse(null);
            String jwt = tokenProvider.generateToken(toDTO(user));

            if (user != null) {
                
                // 记录业务指标
                businessMetrics.incrementUserLogin();
                
                // 异步记录登录信息，不影响登录流程
                try {
                    String ip = request.getHeader("X-Forwarded-For");
                    if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                        ip = request.getRemoteAddr();
                    }
                    String userAgent = request.getHeader("User-Agent");
                    userLoginRecordService.recordLoginAsync(user.getId(), ip, userAgent);
                } catch (Exception e) {
                    log.warn("记录登录信息失败，但不影响用户登录: {}", e.getMessage());
                }
                
                Map<String, Object> response = new HashMap<>();
                response.put("token", jwt);
                response.put("user", toDTO(user));
                response.put("type", "Bearer");
                
                return ResponseEntity.ok(response);
            }
            
            businessMetrics.incrementApiError("user_not_found");
            return ResponseEntity.status(401).body(Collections.singletonMap("error", "用户不存在"));
            
        } catch (Exception e) {
            log.error("登录失败 - 邮箱: {}, 异常类型: {}, 异常信息: {}", 
                loginRequest.get("email"), e.getClass().getSimpleName(), e.getMessage(), e);
            businessMetrics.incrementApiError("authentication_failed");
            return ResponseEntity.status(401).body(Collections.singletonMap("error", "邮箱或密码错误"));
        } finally {
            businessMetrics.recordUserAuth(timer);
        }
    }

    /**
     * 发送验证码
     * 
     * <p>向指定邮箱发送验证码，支持不同类型的验证码。</p>
     * 
     * @param email 邮箱地址
     * @param type 验证码类型（可选）
     * @return 发送成功返回确认信息，失败返回错误信息
     */
    @PostMapping("/send-code")
    @Operation(summary = "发送验证码", description = "向指定邮箱发送验证码")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "邮箱格式错误"),
        @ApiResponse(responseCode = "500", description = "发送失败")
    })
    public ResponseEntity<?> sendEmailCode(
            @Parameter(description = "邮箱地址", required = true)
            @RequestParam String email, 
            @Parameter(description = "验证码类型", example = "register")
            @RequestParam(required = false, defaultValue = "register") String type) {
        
        if (!StringUtils.hasText(email) || !email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
            businessMetrics.incrementApiError("invalid_email");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱格式无效"));
        }
        
        try {
            verificationService.sendEmailCode(email, type);
            return ResponseEntity.ok(Collections.singletonMap("message", "验证码已发送"));
        } catch (Exception e) {
            log.error("发送验证码失败: {}", e.getMessage());
            businessMetrics.incrementApiError("send_code_failed");
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "发送验证码失败"));
        }
    }

    /**
     * 用户注册
     * 
     * <p>通过邮箱验证码注册新用户。</p>
     * 
     * @param payload 注册信息，包含邮箱、密码、验证码
     * @return 注册成功返回用户信息，失败返回错误信息
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "通过邮箱验证码注册新用户")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "注册成功"),
        @ApiResponse(responseCode = "400", description = "参数错误或验证失败")
    })
    public ResponseEntity<?> register(
            @Parameter(description = "注册信息", required = true)
            @RequestBody Map<String, String> payload) {
        
        String email = payload.get("email");
        String password = payload.get("password");
        String emailCode = payload.get("emailCode");
        
        if (!StringUtils.hasText(email) || !StringUtils.hasText(password) || !StringUtils.hasText(emailCode)) {
            businessMetrics.incrementApiError("incomplete_parameters");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "参数不完整"));
        }
        
        if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
            businessMetrics.incrementApiError("invalid_email");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱格式无效"));
        }
        
        if (password.length() < 6) {
            businessMetrics.incrementApiError("weak_password");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "密码长度不能少于6位"));
        }
        
        if (userService.findUserByEmail(email).isPresent()) {
            businessMetrics.incrementApiError("email_exists");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱已被注册"));
        }
        
        if (!verificationService.validateEmailCode(email, emailCode)) {
            businessMetrics.incrementApiError("invalid_code");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱验证码错误或已过期"));
        }
        
        try {
            // 创建用户
            String uuid = idGenerator.generateId();
            User user = userService.registerUser(email, email, password); // username = email
            user.setUuid(uuid);
            userService.saveUser(user);
            UserDTO saved = toDTO(user);
            saved.setPassword(null);
            
            // 记录业务指标
            businessMetrics.incrementUserRegistration();
            
            return ResponseEntity.ok(saved);
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            businessMetrics.incrementApiError("registration_failed");
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "注册失败: " + e.getMessage()));
        }
    }

    /**
     * 重置密码
     * 
     * <p>通过邮箱验证码重置用户密码。</p>
     * 
     * @param payload 重置密码信息，包含邮箱、验证码、新密码
     * @return 重置成功返回确认信息，失败返回错误信息
     */
    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "通过邮箱验证码重置用户密码")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "重置成功"),
        @ApiResponse(responseCode = "400", description = "参数错误或验证失败"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    public ResponseEntity<?> resetPassword(
            @Parameter(description = "重置密码信息", required = true)
            @RequestBody Map<String, String> payload) {
        
        String email = payload.get("email");
        String newPassword = payload.get("newPassword");
        String emailCode = payload.get("emailCode");
        
        if (!StringUtils.hasText(email) || !StringUtils.hasText(newPassword) || !StringUtils.hasText(emailCode)) {
            businessMetrics.incrementApiError("incomplete_parameters");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "参数不完整"));
        }
        
        if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
            businessMetrics.incrementApiError("invalid_email");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱格式无效"));
        }
        
        if (newPassword.length() < 6) {
            businessMetrics.incrementApiError("weak_password");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "密码长度不能少于6位"));
        }
        
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            businessMetrics.incrementApiError("user_not_found");
            return ResponseEntity.status(404).body(Collections.singletonMap("error", "用户不存在"));
        }
        
        if (!verificationService.validateEmailCode(email, emailCode)) {
            businessMetrics.incrementApiError("invalid_code");
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "邮箱验证码错误或已过期"));
        }
        
        try {
            User user = userOpt.get();
            user.setPassword(passwordEncoder.encode(newPassword));
            userService.saveUser(user);
            
            return ResponseEntity.ok(Collections.singletonMap("message", "密码重置成功"));
        } catch (Exception e) {
            log.error("重置密码失败: {}", e.getMessage());
            businessMetrics.incrementApiError("password_reset_failed");
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "重置密码失败"));
        }
    }

    /**
     * 验证reCAPTCHA
     * 
     * @param token reCAPTCHA token
     * @return 验证结果
     */
    @PostMapping("/verify-recaptcha")
    public ResponseEntity<?> verifyRecaptcha(@RequestParam String token) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_FORM_URLENCODED);
            
            String body = "secret=" + recaptchaSecret + "&response=" + token;
            HttpEntity<String> request = new HttpEntity<>(body, headers);
            
            Map<String, Object> response = restTemplate.postForObject(recaptchaUrl, request, Map.class);
            
            if (response != null && Boolean.TRUE.equals(response.get("success"))) {
                return ResponseEntity.ok(Collections.singletonMap("success", true));
            } else {
                return ResponseEntity.badRequest().body(Collections.singletonMap("error", "验证失败"));
            }
        } catch (Exception e) {
            log.error("验证reCAPTCHA失败: {}", e.getMessage());
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "验证服务异常"));
        }
    }

    /**
     * 获取当前用户信息
     * 
     * @return 当前登录用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(401).body(Collections.singletonMap("error", "未登录"));
        }
        
        String username = authentication.getName();
        Optional<User> userOpt = userService.findUserByEmail(username);
        if (userOpt.isEmpty()) {
            userOpt = userService.findUserByUsername(username);
        }
        
        if (userOpt.isPresent()) {
            UserDTO user = toDTO(userOpt.get());
            user.setPassword(null);
            return ResponseEntity.ok(user);
        }
        return ResponseEntity.status(401).body(Collections.singletonMap("error", "用户不存在"));
    }
}