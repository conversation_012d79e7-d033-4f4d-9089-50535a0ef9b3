-- 删除footprints表的trek0_id字段
-- 这个迁移将移除trek0_id字段，使用footprint_id作为唯一标识

-- 首先删除trek0_id字段的唯一约束和索引
ALTER TABLE footprints DROP CONSTRAINT IF EXISTS footprints_trek0_id_key;
DROP INDEX IF EXISTS idx_footprints_trek0_id;

-- 删除trek0_id字段
ALTER TABLE footprints DROP COLUMN IF EXISTS trek0_id;

-- 为footprint_id字段添加非空约束（如果还没有的话）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'footprints' AND column_name = 'footprint_id') THEN
        ALTER TABLE footprints ALTER COLUMN footprint_id SET NOT NULL;
    END IF;
END $$;

-- 确保footprint_id字段有唯一约束（如果不存在的话）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'footprints_footprint_id_key' 
                   AND table_name = 'footprints') THEN
        ALTER TABLE footprints ADD CONSTRAINT footprints_footprint_id_key UNIQUE (footprint_id);
    END IF;
END $$;

-- 为footprint_id字段创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_footprints_footprint_id ON footprints(footprint_id);

-- 更新注释
COMMENT ON COLUMN footprints.footprint_id IS '足迹唯一标识符（使用FootprintIdGenerator生成）';