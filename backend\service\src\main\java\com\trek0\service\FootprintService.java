package com.trek0.service;

import com.trek0.api.dto.CreateFootprintRequest;
import com.trek0.api.dto.FootprintDTO;
import com.trek0.api.dto.UpdateFootprintRequest;
import com.trek0.domain.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 足迹核心服务接口（仅包含CRUD操作）
 */
public interface FootprintService {
    
    /**
     * 创建新的足迹
     */
    FootprintDTO createFootprint(User user, CreateFootprintRequest request);
    
    /**
     * 更新足迹信息
     */
    FootprintDTO updateFootprint(User user, Long footprintId, UpdateFootprintRequest request);
    
    /**
     * 删除足迹
     */
    void deleteFootprint(User user, Long footprintId);
    
    /**
     * 获取足迹详情
     */
    FootprintDTO getFootprint(Long footprintId, User currentUser);
    
    /**
     * 获取用户的所有足迹
     */
    Page<FootprintDTO> getUserFootprints(User user, Pageable pageable);
    
    /**
     * 搜索用户的足迹
     */
    Page<FootprintDTO> searchUserFootprints(User user, String keyword, Pageable pageable);
    
    /**
     * 获取公开的足迹
     */
    Page<FootprintDTO> getPublicFootprints(Pageable pageable);
    
    /**
     * 获取附近的足迹
     */
    List<FootprintDTO> getNearbyFootprints(Double latitude, Double longitude, Double radiusInKm);
    
    /**
     * 检查用户是否有足迹访问权限
     */
    boolean hasAccessPermission(Long footprintId, User user);
    
    /**
     * 检查用户是否有足迹修改权限
     */
    boolean hasEditPermission(Long footprintId, User user);
}