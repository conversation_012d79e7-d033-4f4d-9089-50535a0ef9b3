package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建路径请求DTO
 */
@Schema(description = "创建路径请求")
public class CreatePathRequest {
    @NotBlank(message = "路径名称不能为空")
    @Size(max = 100, message = "路径名称不能超过100个字符")
    @Schema(description = "路径名称", required = true)
    private String name;

    @Size(max = 500, message = "描述不能超过500个字符")
    @Schema(description = "路径描述")
    private String description;

    @NotNull(message = "路径类型不能为空")
    @Schema(description = "路径类型", defaultValue = "walking", allowableValues = {"walking", "cycling", "driving"})
    private String type = "walking";

    @NotEmpty(message = "路径点不能为空")
    @Schema(description = "路径点列表", required = true)
    private List<PathPointRequest> points;

    @Schema(description = "路径距离（米）")
    private Double distance;

    @Schema(description = "持续时间（秒）")
    private Integer duration;

    public CreatePathRequest() {}
    // getter/setter
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public List<PathPointRequest> getPoints() { return points; }
    public void setPoints(List<PathPointRequest> points) { this.points = points; }
    public Double getDistance() { return distance; }
    public void setDistance(Double distance) { this.distance = distance; }
    public Integer getDuration() { return duration; }
    public void setDuration(Integer duration) { this.duration = duration; }
    @Override
    public String toString() {
        return "CreatePathRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", points=" + points +
                ", distance=" + distance +
                ", duration=" + duration +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreatePathRequest that = (CreatePathRequest) o;
        return java.util.Objects.equals(name, that.name) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(type, that.type) &&
                java.util.Objects.equals(points, that.points) &&
                java.util.Objects.equals(distance, that.distance) &&
                java.util.Objects.equals(duration, that.duration);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(name, description, type, points, distance, duration);
    }
    // 内部类 PathPointRequest
    /**
     * 路径点请求内部类
     */
    @Schema(description = "路径点请求信息")
    public static class PathPointRequest {
        @NotNull(message = "纬度不能为空")
        @Schema(description = "纬度", required = true)
        private Double lat;
        @NotNull(message = "经度不能为空")
        @Schema(description = "经度", required = true)
        private Double lng;
        @Schema(description = "海拔高度")
        private Double elevation;
        @Schema(description = "时间戳")
        private LocalDateTime timestamp;
        public PathPointRequest() {}
        public PathPointRequest(Double lat, Double lng, Double elevation, LocalDateTime timestamp) {
            this.lat = lat;
            this.lng = lng;
            this.elevation = elevation;
            this.timestamp = timestamp;
        }
        public Double getLat() { return lat; }
        public void setLat(Double lat) { this.lat = lat; }
        public Double getLng() { return lng; }
        public void setLng(Double lng) { this.lng = lng; }
        public Double getElevation() { return elevation; }
        public void setElevation(Double elevation) { this.elevation = elevation; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        @Override
        public String toString() {
            return "PathPointRequest{" +
                    "lat=" + lat +
                    ", lng=" + lng +
                    ", elevation=" + elevation +
                    ", timestamp=" + timestamp +
                    '}';
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            PathPointRequest that = (PathPointRequest) o;
            return java.util.Objects.equals(lat, that.lat) &&
                    java.util.Objects.equals(lng, that.lng) &&
                    java.util.Objects.equals(elevation, that.elevation) &&
                    java.util.Objects.equals(timestamp, that.timestamp);
        }
        @Override
        public int hashCode() {
            return java.util.Objects.hash(lat, lng, elevation, timestamp);
        }
    }
}
