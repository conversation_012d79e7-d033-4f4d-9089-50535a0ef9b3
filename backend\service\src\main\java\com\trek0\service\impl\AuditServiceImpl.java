package com.trek0.service.impl;

import com.trek0.service.AuditService;
import com.trek0.domain.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 企业级审计服务实现
 * 记录所有安全相关操作，满足合规性要求
 */
@Service
public class AuditServiceImpl implements AuditService {
    
    private static final Logger auditLogger = LoggerFactory.getLogger("AUDIT");
    private static final Logger securityLogger = LoggerFactory.getLogger("SECURITY");
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public void logUserLogin(User user, String ipAddress, String userAgent, boolean success) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] USER_LOGIN | User: %s (ID: %d) | IP: %s | Success: %s | UserAgent: %s",
            timestamp, user.getUsername(), user.getId(), ipAddress, success, userAgent
        );
        auditLogger.info(logMessage);
        
        if (!success) {
            securityLogger.warn("[SECURITY] Failed login attempt for user: {} from IP: {}", user.getUsername(), ipAddress);
        }
    }
    
    @Override
    public void logUserLogout(User user, String ipAddress) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] USER_LOGOUT | User: %s (ID: %d) | IP: %s",
            timestamp, user.getUsername(), user.getId(), ipAddress
        );
        auditLogger.info(logMessage);
    }
    
    @Override
    public void logDataAccess(User user, String resourceType, String resourceId, String action, String ipAddress) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] DATA_ACCESS | User: %s (ID: %d) | Resource: %s/%s | Action: %s | IP: %s",
            timestamp, user.getUsername(), user.getId(), resourceType, resourceId, action, ipAddress
        );
        auditLogger.info(logMessage);
    }
    
    @Override
    public void logDataModification(User user, String resourceType, String resourceId, String action, String details, String ipAddress) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] DATA_MODIFICATION | User: %s (ID: %d) | Resource: %s/%s | Action: %s | Details: %s | IP: %s",
            timestamp, user.getUsername(), user.getId(), resourceType, resourceId, action, details, ipAddress
        );
        auditLogger.info(logMessage);
    }
    
    @Override
    public void logPermissionDenied(User user, String resourceType, String resourceId, String action, String ipAddress) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] PERMISSION_DENIED | User: %s (ID: %d) | Resource: %s/%s | Action: %s | IP: %s",
            timestamp, user.getUsername(), user.getId(), resourceType, resourceId, action, ipAddress
        );
        auditLogger.warn(logMessage);
        securityLogger.warn("[SECURITY] Permission denied for user: {} attempting to access: {}/{}", 
            user.getUsername(), resourceType, resourceId);
    }
    
    @Override
    public void logSecurityException(String exceptionType, String message, String ipAddress, String userAgent) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] SECURITY_EXCEPTION | Type: %s | Message: %s | IP: %s | UserAgent: %s",
            timestamp, exceptionType, message, ipAddress, userAgent
        );
        auditLogger.error(logMessage);
        securityLogger.error("[SECURITY] Security exception: {} - {} from IP: {}", exceptionType, message, ipAddress);
    }
    
    @Override
    public void logApiCall(User user, String endpoint, String method, String ipAddress, int responseCode, long duration) {
        String timestamp = LocalDateTime.now().format(formatter);
        String username = user != null ? user.getUsername() : "ANONYMOUS";
        String userId = user != null ? String.valueOf(user.getId()) : "N/A";
        
        String logMessage = String.format(
            "[AUDIT] [%s] API_CALL | User: %s (ID: %s) | Endpoint: %s %s | Response: %d | Duration: %dms | IP: %s",
            timestamp, username, userId, method, endpoint, responseCode, duration, ipAddress
        );
        auditLogger.info(logMessage);
    }
    
    @Override
    public void logSensitiveOperation(User user, String operation, String details, String ipAddress) {
        String timestamp = LocalDateTime.now().format(formatter);
        String logMessage = String.format(
            "[AUDIT] [%s] SENSITIVE_OPERATION | User: %s (ID: %d) | Operation: %s | Details: %s | IP: %s",
            timestamp, user.getUsername(), user.getId(), operation, details, ipAddress
        );
        auditLogger.warn(logMessage);
        securityLogger.warn("[SECURITY] Sensitive operation performed by user: {} - {} from IP: {}", 
            user.getUsername(), operation, ipAddress);
    }
}