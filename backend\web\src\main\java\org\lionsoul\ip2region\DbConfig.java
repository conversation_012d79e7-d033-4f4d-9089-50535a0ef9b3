package org.lionsoul.ip2region;

import java.io.Serializable;

public class DbConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    private int totalHeaderSize;
    private int indexBlockSize;
    private int segmentIndexSize;
    private int vectorIndexSize;
    private int superBlockSize;
    private int headerBlockSize;
    private int ioBufferSize;
    private int cachePolicy;
    private int fileHandlerPolicy;

    public DbConfig() {
        this.totalHeaderSize = 8192;
        this.indexBlockSize = 12;
        this.segmentIndexSize = 14;
        this.vectorIndexSize = 8;
        this.superBlockSize = 4096;
        this.headerBlockSize = 8192;
        this.ioBufferSize = 4096;
        this.cachePolicy = 0;
        this.fileHandlerPolicy = 0;
    }

    public int getTotalHeaderSize() {
        return totalHeaderSize;
    }

    public int getIndexBlockSize() {
        return indexBlockSize;
    }

    public int getSegmentIndexSize() {
        return segmentIndexSize;
    }

    public int getVectorIndexSize() {
        return vectorIndexSize;
    }

    public int getSuperBlockSize() {
        return superBlockSize;
    }

    public int getHeaderBlockSize() {
        return headerBlockSize;
    }

    public int getIoBufferSize() {
        return ioBufferSize;
    }

    public int getCachePolicy() {
        return cachePolicy;
    }

    public int getFileHandlerPolicy() {
        return fileHandlerPolicy;
    }
}