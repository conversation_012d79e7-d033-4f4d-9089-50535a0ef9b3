-- 创建足迹表
CREATE TABLE IF NOT EXISTS footprints (
    id BIGSERIAL PRIMARY KEY,
    trek0_id VARCHAR(20) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    address VARCHAR(255),
    visibility VARCHAR(20) NOT NULL DEFAULT 'PUBLIC',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_action_method VARCHAR(50),
    CONSTRAINT fk_footprint_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建足迹标签表
CREATE TABLE IF NOT EXISTS footprint_tags (
    footprint_id BIGINT NOT NULL,
    tag VARCHAR(50) NOT NULL,
    PRIMARY KEY (footprint_id, tag),
    CONSTRAINT fk_tag_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(id) ON DELETE CASCADE
);

-- 创建足迹照片表
CREATE TABLE IF NOT EXISTS footprint_photos (
    footprint_id BIGINT NOT NULL,
    photo_url VARCHAR(500) NOT NULL,
    PRIMARY KEY (footprint_id, photo_url),
    CONSTRAINT fk_photo_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(id) ON DELETE CASCADE
);

-- 创建足迹路径表
CREATE TABLE IF NOT EXISTS footprint_paths (
    id BIGSERIAL PRIMARY KEY,
    footprint_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL DEFAULT 'WALKING',
    distance DOUBLE PRECISION,
    duration INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_path_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(id) ON DELETE CASCADE
);

-- 创建路径点表
CREATE TABLE IF NOT EXISTS path_points (
    id BIGSERIAL PRIMARY KEY,
    path_id BIGINT NOT NULL,
    latitude DOUBLE PRECISION NOT NULL,
    longitude DOUBLE PRECISION NOT NULL,
    elevation DOUBLE PRECISION,
    sequence INTEGER NOT NULL,
    timestamp TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_point_path FOREIGN KEY (path_id) REFERENCES footprint_paths(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_footprints_user_id ON footprints(user_id);
CREATE INDEX IF NOT EXISTS idx_footprints_visibility ON footprints(visibility);
CREATE INDEX IF NOT EXISTS idx_footprints_created_at ON footprints(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_footprints_location ON footprints(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_footprint_paths_footprint_id ON footprint_paths(footprint_id);
CREATE INDEX IF NOT EXISTS idx_path_points_path_id ON path_points(path_id);
CREATE INDEX IF NOT EXISTS idx_path_points_sequence ON path_points(path_id, sequence);

-- 添加注释
COMMENT ON TABLE footprints IS '用户足迹表';
COMMENT ON COLUMN footprints.trek0_id IS '唯一标识符';
COMMENT ON COLUMN footprints.user_id IS '用户ID';
COMMENT ON COLUMN footprints.name IS '足迹名称';
COMMENT ON COLUMN footprints.description IS '足迹描述';
COMMENT ON COLUMN footprints.latitude IS '纬度';
COMMENT ON COLUMN footprints.longitude IS '经度';
COMMENT ON COLUMN footprints.address IS '地址';
COMMENT ON COLUMN footprints.visibility IS '可见性：PUBLIC, PRIVATE, FRIENDS';
COMMENT ON COLUMN footprints.last_action_method IS '最后一次编辑或创建足迹的方式';

COMMENT ON TABLE footprint_tags IS '足迹标签表';
COMMENT ON TABLE footprint_photos IS '足迹照片表';

COMMENT ON TABLE footprint_paths IS '足迹路径表';
COMMENT ON COLUMN footprint_paths.type IS '路径类型：WALKING, CYCLING, DRIVING';
COMMENT ON COLUMN footprint_paths.distance IS '距离（米）';
COMMENT ON COLUMN footprint_paths.duration IS '时长（秒）';

COMMENT ON TABLE path_points IS '路径点表';
COMMENT ON COLUMN path_points.elevation IS '海拔（米）';
COMMENT ON COLUMN path_points.sequence IS '点的顺序';
COMMENT ON COLUMN path_points.timestamp IS '记录时间'; 