-- 企业标准足迹标签表 - 重构版本

CREATE TABLE footprint_tags (
    tag_id BIGSERIAL PRIMARY KEY,
    footprint_id BIGINT NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    tag_category VARCHAR(30),
    tag_color VARCHAR(7),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_tag_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(footprint_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_tag_name_length CHECK (LENGTH(tag_name) >= 1 AND LENGTH(tag_name) <= 50),
    CONSTRAINT chk_tag_color_format CHECK (tag_color IS NULL OR tag_color ~* '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT uk_footprint_tag UNIQUE (footprint_id, tag_name)
);

-- 企业级索引
CREATE INDEX idx_footprint_tags_footprint_id ON footprint_tags(footprint_id);
CREATE INDEX idx_footprint_tags_name ON footprint_tags(tag_name);
CREATE INDEX idx_footprint_tags_category ON footprint_tags(tag_category);

-- 企业级注释
COMMENT ON TABLE footprint_tags IS '企业标准足迹标签表';
COMMENT ON COLUMN footprint_tags.tag_id IS '标签主键ID';
COMMENT ON COLUMN footprint_tags.footprint_id IS '足迹ID（外键）';
COMMENT ON COLUMN footprint_tags.tag_name IS '标签名称（1-50字符）';
COMMENT ON COLUMN footprint_tags.tag_category IS '标签分类';
COMMENT ON COLUMN footprint_tags.tag_color IS '标签颜色（十六进制）';
COMMENT ON COLUMN footprint_tags.created_at IS '标签创建时间';