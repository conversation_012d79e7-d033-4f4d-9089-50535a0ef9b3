package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

@Entity
@Table(name = "footprint_photos")
public class FootprintPhoto {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "photo_id")
    private Long photoId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "footprint_id", nullable = false)
    @JsonBackReference
    private Footprint footprint;
    
    @NotBlank(message = "图片URL不能为空")
    @Size(max = 500, message = "图片URL不能超过500个字符")
    @Column(name = "photo_url", nullable = false, length = 500)
    private String photoUrl;
    
    @Size(max = 255, message = "图片文件名不能超过255个字符")
    @Column(name = "original_filename", length = 255)
    private String originalFilename;
    
    @Min(value = 1, message = "图片宽度必须大于0")
    @Column(name = "image_width")
    private Integer imageWidth;
    
    @Min(value = 1, message = "图片高度必须大于0")
    @Column(name = "image_height")
    private Integer imageHeight;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "image_format", length = 10)
    private ImageFormat imageFormat;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "upload_method", nullable = false, length = 30)
    private UploadMethod uploadMethod = UploadMethod.WEB;
    
    @Column(name = "photo_description", columnDefinition = "TEXT")
    private String photoDescription;
    
    @Column(name = "display_order", nullable = false)
    private Integer displayOrder = 0;
    
    @Column(name = "is_cover_photo", nullable = false)
    private Boolean isCoverPhoto = false;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "photo_status", nullable = false, length = 20)
    private PhotoStatus photoStatus = PhotoStatus.ACTIVE;
    
    @Column(name = "uploaded_at", nullable = false)
    private LocalDateTime uploadedAt;
    
    @Column(name = "processed_at")
    private LocalDateTime processedAt;
    
    // 枚举定义
    public enum ImageFormat {
        JPG, JPEG, PNG, WEBP, GIF
    }
    
    public enum UploadMethod {
        WEB, MOBILE, API, BATCH
    }
    
    public enum PhotoStatus {
        ACTIVE, PROCESSING, FAILED, DELETED
    }
    
    @PrePersist
    protected void onCreate() {
        uploadedAt = LocalDateTime.now();
    }
    
    // Constructors
    public FootprintPhoto() {}
    
    public FootprintPhoto(Footprint footprint, String photoUrl) {
        this.footprint = footprint;
        this.photoUrl = photoUrl;
    }
    
    public FootprintPhoto(Footprint footprint, String photoUrl, String originalFilename) {
        this.footprint = footprint;
        this.photoUrl = photoUrl;
        this.originalFilename = originalFilename;
    }
    
    // Getters and Setters
    public Long getPhotoId() {
        return photoId;
    }
    
    public void setPhotoId(Long photoId) {
        this.photoId = photoId;
    }
    
    public Footprint getFootprint() {
        return footprint;
    }
    
    public void setFootprint(Footprint footprint) {
        this.footprint = footprint;
    }
    
    public String getPhotoUrl() {
        return photoUrl;
    }
    
    public void setPhotoUrl(String photoUrl) {
        this.photoUrl = photoUrl;
    }
    
    public String getOriginalFilename() {
        return originalFilename;
    }
    
    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    
    public Integer getImageWidth() {
        return imageWidth;
    }
    
    public void setImageWidth(Integer imageWidth) {
        this.imageWidth = imageWidth;
    }
    
    public Integer getImageHeight() {
        return imageHeight;
    }
    
    public void setImageHeight(Integer imageHeight) {
        this.imageHeight = imageHeight;
    }
    
    public ImageFormat getImageFormat() {
        return imageFormat;
    }
    
    public void setImageFormat(ImageFormat imageFormat) {
        this.imageFormat = imageFormat;
    }
    
    public UploadMethod getUploadMethod() {
        return uploadMethod;
    }
    
    public void setUploadMethod(UploadMethod uploadMethod) {
        this.uploadMethod = uploadMethod;
    }
    
    public String getPhotoDescription() {
        return photoDescription;
    }
    
    public void setPhotoDescription(String photoDescription) {
        this.photoDescription = photoDescription;
    }
    
    public Integer getDisplayOrder() {
        return displayOrder;
    }
    
    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }
    
    public Boolean getIsCoverPhoto() {
        return isCoverPhoto;
    }
    
    public void setIsCoverPhoto(Boolean isCoverPhoto) {
        this.isCoverPhoto = isCoverPhoto;
    }
    
    public PhotoStatus getPhotoStatus() {
        return photoStatus;
    }
    
    public void setPhotoStatus(PhotoStatus photoStatus) {
        this.photoStatus = photoStatus;
    }
    
    public LocalDateTime getUploadedAt() {
        return uploadedAt;
    }
    
    public void setUploadedAt(LocalDateTime uploadedAt) {
        this.uploadedAt = uploadedAt;
    }
    
    public LocalDateTime getProcessedAt() {
        return processedAt;
    }
    
    public void setProcessedAt(LocalDateTime processedAt) {
        this.processedAt = processedAt;
    }
    
    // 业务方法
    public boolean isProcessed() {
        return processedAt != null;
    }
    
    public void markAsProcessed() {
        this.processedAt = LocalDateTime.now();
        this.photoStatus = PhotoStatus.ACTIVE;
    }
    
    public void markAsFailed() {
        this.photoStatus = PhotoStatus.FAILED;
    }
    
    public void markAsDeleted() {
        this.photoStatus = PhotoStatus.DELETED;
    }
    
    public boolean isActive() {
        return PhotoStatus.ACTIVE.equals(this.photoStatus);
    }
    
    public void setCoverPhoto(boolean isCover) {
        this.isCoverPhoto = isCover;
    }
    
    public String getImageUrl() { return photoUrl; }
    
    @Override
    public String toString() {
        return "FootprintPhoto{" +
                "photoId=" + photoId +
                ", photoUrl='" + photoUrl + '\'' +
                ", originalFilename='" + originalFilename + '\'' +
                ", imageWidth=" + imageWidth +
                ", imageHeight=" + imageHeight +
                ", imageFormat=" + imageFormat +
                ", uploadMethod=" + uploadMethod +
                ", displayOrder=" + displayOrder +
                ", isCoverPhoto=" + isCoverPhoto +
                ", photoStatus=" + photoStatus +
                ", uploadedAt=" + uploadedAt +
                ", processedAt=" + processedAt +
                '}';
    }
}