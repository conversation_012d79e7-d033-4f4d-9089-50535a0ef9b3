# 开发环境配置
spring:
  datasource:
    url: ****************************************
    username: ${DB_USERNAME:postgres}
    password: 080706
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none  # 禁用Hibernate自动DDL，使用Flyway管理
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        naming:
          physical-strategy: org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy
        cache:
          use_second_level_cache: false
          use_query_cache: false
  flyway:
    enabled: true
    clean-on-validation-error: true
    clean-disabled: false
    baseline-on-migrate: true
    locations: classpath:db/migration
    sql-migration-prefix: V
    sql-migration-separator: __
    sql-migration-suffixes: .sql

logging:
  level:
    com.trek0.api: DEBUG
    org.springframework.web: DEBUG
    io.github.resilience4j: DEBUG
    
trek0:
  security:
    login:
      max-attempts: 10 # 开发环境放宽限制