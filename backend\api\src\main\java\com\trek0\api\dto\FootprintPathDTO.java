package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 足迹路径DTO
 */
@Schema(description = "足迹路径信息")
public class FootprintPathDTO {
    @Schema(description = "路径ID")
    private Long id;
    @NotBlank(message = "路径名称不能为空")
    @Size(max = 100, message = "路径名称长度不能超过100个字符")
    @Schema(description = "路径名称", required = true)
    private String name;
    @Size(max = 500, message = "路径描述长度不能超过500个字符")
    @Schema(description = "路径描述")
    private String description;
    @Schema(description = "路径类型")
    private String type;
    @Schema(description = "路径点列表")
    private List<PathPointDTO> points;
    @Schema(description = "路径距离（米）")
    private Double distance;
    @Schema(description = "持续时间（秒）")
    private Integer duration;
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    public FootprintPathDTO() {}
    // getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public List<PathPointDTO> getPoints() { return points; }
    public void setPoints(List<PathPointDTO> points) { this.points = points; }
    public Double getDistance() { return distance; }
    public void setDistance(Double distance) { this.distance = distance; }
    public Integer getDuration() { return duration; }
    public void setDuration(Integer duration) { this.duration = duration; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    @Override
    public String toString() {
        return "FootprintPathDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", points=" + points +
                ", distance=" + distance +
                ", duration=" + duration +
                ", createdAt=" + createdAt +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FootprintPathDTO that = (FootprintPathDTO) o;
        return java.util.Objects.equals(id, that.id) &&
                java.util.Objects.equals(name, that.name) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(type, that.type) &&
                java.util.Objects.equals(points, that.points) &&
                java.util.Objects.equals(distance, that.distance) &&
                java.util.Objects.equals(duration, that.duration) &&
                java.util.Objects.equals(createdAt, that.createdAt);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(id, name, description, type, points, distance, duration, createdAt);
    }
    /**
     * 路径点DTO内部类
     */
    @Schema(description = "路径点信息")
    public static class PathPointDTO {
        @NotNull(message = "纬度不能为空")
        @Schema(description = "纬度", required = true)
        private Double lat;
        @NotNull(message = "经度不能为空")
        @Schema(description = "经度", required = true)
        private Double lng;
        @Schema(description = "海拔高度")
        private Double elevation;
        @Schema(description = "时间戳")
        private LocalDateTime timestamp;
        public PathPointDTO() {}
        public PathPointDTO(Double lat, Double lng, Double elevation, LocalDateTime timestamp) {
            this.lat = lat;
            this.lng = lng;
            this.elevation = elevation;
            this.timestamp = timestamp;
        }
        public Double getLat() { return lat; }
        public void setLat(Double lat) { this.lat = lat; }
        public Double getLng() { return lng; }
        public void setLng(Double lng) { this.lng = lng; }
        public Double getElevation() { return elevation; }
        public void setElevation(Double elevation) { this.elevation = elevation; }
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        @Override
        public String toString() {
            return "PathPointDTO{" +
                    "lat=" + lat +
                    ", lng=" + lng +
                    ", elevation=" + elevation +
                    ", timestamp=" + timestamp +
                    '}';
        }
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            PathPointDTO that = (PathPointDTO) o;
            return java.util.Objects.equals(lat, that.lat) &&
                    java.util.Objects.equals(lng, that.lng) &&
                    java.util.Objects.equals(elevation, that.elevation) &&
                    java.util.Objects.equals(timestamp, that.timestamp);
        }
        @Override
        public int hashCode() {
            return java.util.Objects.hash(lat, lng, elevation, timestamp);
        }
    }

    public static FootprintPathDTO fromEntity(com.trek0.domain.model.FootprintPath entity) {
        if (entity == null) return null;
        FootprintPathDTO dto = new FootprintPathDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getPathName());
        dto.setDescription(entity.getPathDescription());
        dto.setType(entity.getPathType() != null ? entity.getPathType().name() : null);
        dto.setDistance(entity.getTotalDistanceMeters() != null ? entity.getTotalDistanceMeters().doubleValue() : null);
        dto.setDuration(entity.getTotalDurationSeconds());
        dto.setCreatedAt(entity.getCreatedAt());
        // 可补充points等映射
        return dto;
    }
}
