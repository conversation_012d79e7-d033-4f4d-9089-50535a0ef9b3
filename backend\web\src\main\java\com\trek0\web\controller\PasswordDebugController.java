package com.trek0.web.controller;

import com.trek0.api.dto.UserDTO;
import com.trek0.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import com.trek0.domain.model.User;

@RestController
@RequestMapping("/api/debug")
public class PasswordDebugController {
    
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;
    
    @Autowired
    public PasswordDebugController(UserService userService, PasswordEncoder passwordEncoder) {
        this.userService = userService;
        this.passwordEncoder = passwordEncoder;
    }
    
    private UserDTO toDTO(User user) {
        if (user == null) return null;
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setEmail(user.getEmail());
        dto.setUsername(user.getUsername());
        dto.setPassword(user.getPassword());
        dto.setUuid(user.getUuid());
        dto.setIpLocation(user.getIpLocation());
        dto.setProfileImage(user.getProfileImageUrl() != null ? user.getProfileImageUrl() : user.getProfileImage());
        dto.setSignature(user.getUserSignature());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
    private User toEntity(UserDTO dto) {
        if (dto == null) return null;
        User user = new User();
        user.setId(dto.getId());
        user.setEmail(dto.getEmail());
        user.setUsername(dto.getUsername());
        user.setPassword(dto.getPassword());
        user.setUuid(dto.getUuid());
        user.setIpLocation(dto.getIpLocation());
        user.setProfileImageUrl(dto.getProfileImage());
        user.setUserSignature(dto.getSignature());
        user.setCreatedAt(dto.getCreatedAt());
        user.setUpdatedAt(dto.getUpdatedAt());
        return user;
    }
    
    @GetMapping("/check-password/{email}")
    public ResponseEntity<?> checkPassword(@PathVariable String email) {
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        User user = userOpt.get();
        UserDTO userDTO = toDTO(user);
        Map<String, Object> response = new HashMap<>();
        response.put("email", userDTO.getEmail());
        response.put("passwordHash", userDTO.getPassword());
        response.put("passwordLength", userDTO.getPassword().length());
        response.put("isBCryptHash", userDTO.getPassword().startsWith("$2"));
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/fix-password")
    public ResponseEntity<?> fixPassword(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String plainPassword = request.get("password");
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        User user = userOpt.get();
        String oldPassword = user.getPassword();
        if (user.getPassword().startsWith("$2")) {
            boolean matches = passwordEncoder.matches(plainPassword, user.getPassword());
            Map<String, Object> response = new HashMap<>();
            response.put("message", "密码已经是BCrypt格式");
            response.put("matches", matches);
            return ResponseEntity.ok(response);
        }
        String encodedPassword = passwordEncoder.encode(plainPassword);
        user.setPassword(encodedPassword);
        userService.saveUser(user);
        Map<String, Object> response = new HashMap<>();
        response.put("message", "密码已更新为BCrypt格式");
        response.put("oldPassword", oldPassword);
        response.put("newPassword", encodedPassword);
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/test-login")
    public ResponseEntity<?> testLogin(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        Optional<User> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        User user = userOpt.get();
        boolean matches = passwordEncoder.matches(password, user.getPassword());
        Map<String, Object> response = new HashMap<>();
        response.put("email", email);
        response.put("passwordMatches", matches);
        response.put("storedPasswordHash", user.getPassword());
        return ResponseEntity.ok(response);
    }
}