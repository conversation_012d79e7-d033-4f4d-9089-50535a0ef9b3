package com.trek0.infra.cache;

import org.springframework.stereotype.Component;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class RedisCacheManager {
    private final ConcurrentHashMap<String, Object> cache = new ConcurrentHashMap<>();
    public void put(String key, Object value) { cache.put(key, value); }
    public Object get(String key) { return cache.get(key); }
    public void evict(String key) { cache.remove(key); }
} 