# Domain Module

## 职责说明
Domain模块是系统的领域层，包含：
- 领域模型（实体、值对象）
- 聚合根
- Repository接口定义
- 领域服务接口
- 领域事件

## 模块依赖
- 依赖：`api`模块
- 被依赖：`service`、`infra`模块

## 包结构
```
com.trek0.domain/
├── model/         # 领域模型
│   ├── entity/    # 实体
│   └── value/     # 值对象
├── repository/    # 仓储接口
├── service/       # 领域服务接口
└── event/         # 领域事件
```

## 核心模型
- **User**: 用户聚合根
- **Footprint**: 足迹聚合根
- **FootprintPath**: 足迹路径
- **FootprintPhoto**: 足迹照片
- **UserFollow**: 用户关注关系

## 开发规范
- 领域模型不依赖基础设施层
- Repository接口定义在domain层，实现在infra层
- 保持领域模型的纯净性，不包含技术实现细节
- 使用DDD（领域驱动设计）原则 