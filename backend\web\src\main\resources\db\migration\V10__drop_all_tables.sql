-- 删除所有现有表 - 企业标准重构
-- 注意：此操作将删除所有数据，请确保已备份重要数据

-- 删除外键约束和索引（按依赖关系顺序）
DROP TABLE IF EXISTS path_points CASCADE;
DROP TABLE IF EXISTS footprint_paths CASCADE;
DROP TABLE IF EXISTS footprint_photos CASCADE;
DROP TABLE IF EXISTS footprint_tags CASCADE;
DROP TABLE IF EXISTS footprints CASCADE;
DROP TABLE IF EXISTS user_follows CASCADE;
DROP TABLE IF EXISTS user_login_records CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- 删除可能存在的序列
DROP SEQUENCE IF EXISTS users_id_seq CASCADE;
DROP SEQUENCE IF EXISTS footprints_id_seq CASCADE;
DROP SEQUENCE IF EXISTS footprint_paths_id_seq CASCADE;
DROP SEQUENCE IF EXISTS path_points_id_seq CASCADE;
DROP SEQUENCE IF EXISTS user_login_records_id_seq CASCADE;

-- 删除可能存在的函数
DROP FUNCTION IF EXISTS generate_temp_footprint_id(BIGINT) CASCADE;
DROP FUNCTION IF EXISTS generate_temp_path_id(BIGINT) CASCADE;

-- 添加注释表示清理完成
COMMENT ON SCHEMA public IS '企业标准数据库重构 - 所有表已清理';