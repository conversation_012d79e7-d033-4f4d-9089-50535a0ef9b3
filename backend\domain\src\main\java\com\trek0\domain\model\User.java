package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class User {
    private Long id;
    private String username;
    private String email;
    private String password;
    private String profileImage;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String nickname;
    private AccountStatus accountStatus = AccountStatus.ACTIVE;
    private java.time.LocalDateTime lastLoginTime;
    private String uuid;
    private String ipLocation;
    private String profileImageUrl;
    private String userSignature;
    private java.util.List<User> followers = new java.util.ArrayList<>();

    public enum AccountStatus {
        ACTIVE, SUSPENDED, DELETED
    }

    // getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    public String getProfileImage() { return profileImage; }
    public void setProfileImage(String profileImage) { this.profileImage = profileImage; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    public String getNickname() { return nickname; }
    public void setNickname(String nickname) { this.nickname = nickname; }
    public AccountStatus getAccountStatus() { return accountStatus; }
    public void setAccountStatus(AccountStatus accountStatus) { this.accountStatus = accountStatus; }
    public java.time.LocalDateTime getLastLoginTime() { return lastLoginTime; }
    public void setLastLoginTime(java.time.LocalDateTime lastLoginTime) { this.lastLoginTime = lastLoginTime; }
    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }
    public String getIpLocation() { return ipLocation; }
    public void setIpLocation(String ipLocation) { this.ipLocation = ipLocation; }
    public String getProfileImageUrl() { return profileImageUrl; }
    public void setProfileImageUrl(String profileImageUrl) { this.profileImageUrl = profileImageUrl; }
    public String getUserSignature() { return userSignature; }
    public void setUserSignature(String userSignature) { this.userSignature = userSignature; }
    public java.util.List<User> getFollowers() { return followers; }
    public void setFollowers(java.util.List<User> followers) { this.followers = followers; }
    public String getUserUuid() { return getUuid(); }
    public void setUserUuid(String uuid) { setUuid(uuid); }
    public String getLastIpLocation() { return getIpLocation(); }
    public void setLastIpLocation(String ipLocation) { setIpLocation(ipLocation); }
}