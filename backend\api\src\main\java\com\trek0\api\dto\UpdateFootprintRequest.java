package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;

/**
 * 更新足迹请求DTO
 */
@Schema(description = "更新足迹请求")
public class UpdateFootprintRequest {
    @Size(max = 100, message = "足迹名称不能超过100个字符")
    @Schema(description = "足迹名称")
    private String name;
    @Size(max = 500, message = "足迹描述不能超过500个字符")
    @Schema(description = "足迹描述")
    private String description;
    @Size(max = 255, message = "地址不能超过255个字符")
    @Schema(description = "地址")
    private String address;
    @Schema(description = "可见性")
    private String visibility;
    @Schema(description = "纬度")
    private Double lat;
    @Schema(description = "经度")
    private Double lng;
    @Schema(description = "位置选择方式")
    private String locationMode;
    public UpdateFootprintRequest() {}
    // getter/setter
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    public String getVisibility() { return visibility; }
    public void setVisibility(String visibility) { this.visibility = visibility; }
    public Double getLat() { return lat; }
    public void setLat(Double lat) { this.lat = lat; }
    public Double getLng() { return lng; }
    public void setLng(Double lng) { this.lng = lng; }
    public String getLocationMode() { return locationMode; }
    public void setLocationMode(String locationMode) { this.locationMode = locationMode; }
    @Override
    public String toString() {
        return "UpdateFootprintRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", address='" + address + '\'' +
                ", visibility='" + visibility + '\'' +
                ", lat=" + lat +
                ", lng=" + lng +
                ", locationMode='" + locationMode + '\'' +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UpdateFootprintRequest that = (UpdateFootprintRequest) o;
        return java.util.Objects.equals(name, that.name) &&
                java.util.Objects.equals(description, that.description) &&
                java.util.Objects.equals(address, that.address) &&
                java.util.Objects.equals(visibility, that.visibility) &&
                java.util.Objects.equals(lat, that.lat) &&
                java.util.Objects.equals(lng, that.lng) &&
                java.util.Objects.equals(locationMode, that.locationMode);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(name, description, address, visibility, lat, lng, locationMode);
    }
}
