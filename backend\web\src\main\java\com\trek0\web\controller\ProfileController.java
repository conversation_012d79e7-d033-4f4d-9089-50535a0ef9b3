package com.trek0.web.controller;

import com.trek0.api.dto.UserDTO;
import com.trek0.service.UserService;
import com.trek0.common.util.IpLocationUtil;
import com.trek0.domain.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.servlet.http.HttpServletRequest;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/users")
public class ProfileController {
    private final UserService userService;

    @Autowired
    public ProfileController(UserService userService) {
        this.userService = userService;
    }

    private UserDTO toDTO(User user) {
        if (user == null) return null;
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setEmail(user.getEmail());
        dto.setUsername(user.getUsername());
        dto.setPassword(user.getPassword());
        dto.setUuid(user.getUuid());
        dto.setIpLocation(user.getIpLocation());
        dto.setProfileImage(user.getProfileImageUrl() != null ? user.getProfileImageUrl() : user.getProfileImage());
        dto.setSignature(user.getUserSignature());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
    private User toEntity(UserDTO dto) {
        if (dto == null) return null;
        User user = new User();
        user.setId(dto.getId());
        user.setEmail(dto.getEmail());
        user.setUsername(dto.getUsername());
        user.setPassword(dto.getPassword());
        user.setUuid(dto.getUuid());
        user.setIpLocation(dto.getIpLocation());
        user.setProfileImageUrl(dto.getProfileImage());
        user.setUserSignature(dto.getSignature());
        user.setCreatedAt(dto.getCreatedAt());
        user.setUpdatedAt(dto.getUpdatedAt());
        return user;
    }

    @GetMapping("/profile/{id}")
    public ResponseEntity<UserDTO> getUser(@PathVariable Long id, HttpServletRequest request) {
        return userService.findUserById(id)
            .map(user -> {
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
                    ip = "*******";
                }
                userService.updateUserIpLocation(user, ip);
                return ResponseEntity.ok(toDTO(user));
            })
            .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/profile/{id}")
    public ResponseEntity<UserDTO> updateUser(@PathVariable Long id, @RequestBody UserDTO userDto) {
        return userService.findUserById(id)
                .map(existingUser -> {
                    if (userDto.getUsername() != null) existingUser.setUsername(userDto.getUsername());
                    if (userDto.getSignature() != null) existingUser.setUserSignature(userDto.getSignature());
                    User saved = userService.saveUser(existingUser);
                    return ResponseEntity.ok(toDTO(saved));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/profile/{id}/avatar")
    public ResponseEntity<?> uploadAvatar(@PathVariable Long id, @RequestParam("file") MultipartFile file) {
        try {
            Optional<User> userOpt = userService.findUserById(id);
            if (userOpt.isEmpty()) {
                return ResponseEntity.notFound().build();
            }
            User user = userOpt.get();
            // 保存文件到本地
            String uploadDir = "uploads/avatars/";
            Files.createDirectories(Paths.get(uploadDir));
            String filename = "avatar_" + id + "_" + System.currentTimeMillis() + ".jpg";
            Path filePath = Paths.get(uploadDir, filename);
            file.transferTo(filePath);
            // 构造图片URL
            String imageUrl = "/uploads/avatars/" + filename;
            user.setProfileImageUrl(imageUrl);
            userService.saveUser(user);
            Map<String, String> result = new HashMap<>();
            result.put("profileImage", imageUrl);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("头像上传失败");
        }
    }

    @PutMapping("/profile/{id}/ip-location")
    public ResponseEntity<?> updateIpLocation(@PathVariable Long id, @RequestBody Map<String, String> body) {
        String ipLocation = body.get("ipLocation");
        if (ipLocation == null) {
            return ResponseEntity.badRequest().body(Map.of("error", "ipLocation 不能为空"));
        }
        Optional<User> userOpt = userService.findUserById(id);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        User user = userOpt.get();
        user.setIpLocation(ipLocation);
        userService.saveUser(user);
        return ResponseEntity.ok("IP属地已更新");
    }

    @PutMapping("/profile/{id}/change-password")
    public ResponseEntity<?> changePassword(@PathVariable Long id, @RequestBody Map<String, String> body) {
        String oldPassword = body.get("oldPassword");
        String newPassword = body.get("newPassword");
        if (oldPassword == null || newPassword == null) {
            return ResponseEntity.badRequest().body("参数不完整");
        }
        boolean ok = userService.changePassword(id, oldPassword, newPassword);
        if (ok) return ResponseEntity.ok().body("密码修改成功");
        return ResponseEntity.status(400).body("原密码错误");
    }
}