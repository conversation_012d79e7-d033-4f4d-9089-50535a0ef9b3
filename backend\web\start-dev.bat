@echo off
REM Trek0 开发环境启动脚本
REM 请根据您的PostgreSQL配置修改以下变量

echo === Trek0 开发环境启动脚本 ===

REM 设置数据库环境变量
set DB_USERNAME=postgres
set DB_PASSWORD=080706
REM 请修改上面的密码为您的PostgreSQL密码

REM 设置JWT密钥
set JWT_SECRET=trek0-jwt-secret-key-for-development

REM 设置文件上传目录
set UPLOAD_DIR=./uploads

echo 环境变量已设置:
echo DB_USERNAME: %DB_USERNAME%
echo DB_PASSWORD: %DB_PASSWORD%
echo JWT_SECRET: %JWT_SECRET%
echo UPLOAD_DIR: %UPLOAD_DIR%

echo.
echo 正在启动应用...
echo 请确保PostgreSQL服务正在运行，并且已创建 trek0_dev 数据库

REM 启动应用
mvn spring-boot:run

pause 