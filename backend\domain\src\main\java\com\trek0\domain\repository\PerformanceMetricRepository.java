package com.trek0.domain.repository;

import com.trek0.domain.model.PerformanceMetric;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PerformanceMetricRepository extends JpaRepository<PerformanceMetric, Long> {
    
    /**
     * 根据指标名称查找最新的指标
     */
    Optional<PerformanceMetric> findFirstByMetricNameOrderByRecordTimeDesc(String metricName);
    
    /**
     * 根据指标名称查找所有指标（分页）
     */
    Page<PerformanceMetric> findByMetricNameOrderByRecordTimeDesc(String metricName, Pageable pageable);
    
    /**
     * 根据指标分类查找指标
     */
    Page<PerformanceMetric> findByMetricCategoryOrderByRecordTimeDesc(String metricCategory, Pageable pageable);
    
    /**
     * 根据时间范围查找指标
     */
    List<PerformanceMetric> findByRecordTimeBetweenOrderByRecordTimeDesc(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据指标名称和时间范围查找指标
     */
    List<PerformanceMetric> findByMetricNameAndRecordTimeBetweenOrderByRecordTimeDesc(String metricName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据指标分类和时间范围查找指标
     */
    List<PerformanceMetric> findByMetricCategoryAndRecordTimeBetweenOrderByRecordTimeDesc(String metricCategory, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找指定时间范围内的平均值
     */
    @Query("SELECT AVG(p.metricValue) FROM PerformanceMetric p WHERE p.metricName = :metricName AND p.recordTime BETWEEN :startTime AND :endTime")
    Double findAverageValueByMetricNameAndTimeRange(@Param("metricName") String metricName, 
                                                   @Param("startTime") LocalDateTime startTime, 
                                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找指定时间范围内的最大值
     */
    @Query("SELECT MAX(p.metricValue) FROM PerformanceMetric p WHERE p.metricName = :metricName AND p.recordTime BETWEEN :startTime AND :endTime")
    Double findMaxValueByMetricNameAndTimeRange(@Param("metricName") String metricName, 
                                               @Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找指定时间范围内的最小值
     */
    @Query("SELECT MIN(p.metricValue) FROM PerformanceMetric p WHERE p.metricName = :metricName AND p.recordTime BETWEEN :startTime AND :endTime")
    Double findMinValueByMetricNameAndTimeRange(@Param("metricName") String metricName, 
                                               @Param("startTime") LocalDateTime startTime, 
                                               @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的指标数量
     */
    long countByMetricNameAndRecordTimeBetween(String metricName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找所有不同的指标名称
     */
    @Query("SELECT DISTINCT p.metricName FROM PerformanceMetric p ORDER BY p.metricName")
    List<String> findDistinctMetricNames();
    
    /**
     * 查找所有不同的指标分类
     */
    @Query("SELECT DISTINCT p.metricCategory FROM PerformanceMetric p WHERE p.metricCategory IS NOT NULL ORDER BY p.metricCategory")
    List<String> findDistinctMetricCategories();
    
    /**
     * 删除指定时间之前的指标数据
     */
    void deleteByRecordTimeBefore(LocalDateTime cutoffTime);
    
    /**
     * 查找最近的指标数据
     */
    List<PerformanceMetric> findTop10ByOrderByRecordTimeDesc();
}