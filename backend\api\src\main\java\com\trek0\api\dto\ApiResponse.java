package com.trek0.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 通用API响应DTO
 */
public class ApiResponse<T> {
    @Schema(description = "响应码", example = "200")
    private Integer code;
    @Schema(description = "响应消息", example = "操作成功")
    private String message;
    @Schema(description = "响应数据")
    private T data;
    @Schema(description = "时间戳")
    private Long timestamp;

    public ApiResponse() {}
    public ApiResponse(Integer code, String message, T data, Long timestamp) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = timestamp;
    }
    public Integer getCode() { return code; }
    public void setCode(Integer code) { this.code = code; }
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    public T getData() { return data; }
    public void setData(T data) { this.data = data; }
    public Long getTimestamp() { return timestamp; }
    public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApiResponse<?> that = (ApiResponse<?>) o;
        return java.util.Objects.equals(code, that.code) &&
                java.util.Objects.equals(message, that.message) &&
                java.util.Objects.equals(data, that.data) &&
                java.util.Objects.equals(timestamp, that.timestamp);
    }
    @Override
    public int hashCode() {
        return java.util.Objects.hash(code, message, data, timestamp);
    }
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data, System.currentTimeMillis());
    }
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data, System.currentTimeMillis());
    }
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null, System.currentTimeMillis());
    }
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(500, message, null, System.currentTimeMillis());
    }
}
