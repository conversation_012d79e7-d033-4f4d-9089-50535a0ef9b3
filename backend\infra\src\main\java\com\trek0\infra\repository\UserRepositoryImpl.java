package com.trek0.infra.repository;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public class UserRepositoryImpl implements UserRepository {
    private final Map<Long, User> db = new HashMap<>();
    private long idSeq = 1;

    @Override
    public Optional<User> findById(Long id) {
        return Optional.ofNullable(db.get(id));
    }

    @Override
    public Optional<User> findByEmail(String email) {
        return db.values().stream().filter(u -> u.getEmail().equals(email)).findFirst();
    }

    @Override
    public List<User> findAll() {
        return new ArrayList<>(db.values());
    }

    @Override
    public User save(User user) {
        if (user.getId() == null) {
            user.setId(idSeq++);
        }
        db.put(user.getId(), user);
        return user;
    }

    @Override
    public void deleteById(Long id) {
        db.remove(id);
    }

    @Override
    public List<User> findActiveUsers(java.time.LocalDateTime cutoffTime) {
        List<User> result = new ArrayList<>();
        for (User user : db.values()) {
            if (user.getAccountStatus() == User.AccountStatus.ACTIVE
                && user.getLastLoginTime() != null
                && user.getLastLoginTime().isAfter(cutoffTime)) {
                result.add(user);
            }
        }
        return result;
    }

    @Override
    public Optional<User> findByUsername(String username) {
        return db.values().stream().filter(u -> username.equals(u.getUsername())).findFirst();
    }
} 