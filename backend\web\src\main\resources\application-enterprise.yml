# Trek0 API 企业级配置文件
# 主配置文件，整合所有企业级功能

server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 连接超时配置
  connection-timeout: 20000
  # 压缩配置
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

spring:
  application:
    name: trek0-api
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true
      
  # Jackson配置
  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      indent-output: true
    deserialization:
      fail-on-unknown-properties: false
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# 应用自定义配置
trek0:
  # 文件存储配置
  file:
    upload-dir: ${UPLOAD_DIR:./uploads}
    max-size: 10MB
    allowed-types: jpg,jpeg,png,gif,webp
    
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:trek0-jwt-secret-key-for-development-only}
    expiration: 86400000 # 24小时
    refresh-expiration: 604800000 # 7天
    
  # 邮件配置
  mail:
    enabled: ${MAIL_ENABLED:false}
    from: ${MAIL_FROM:<EMAIL>}
    
  # 短信配置
  sms:
    enabled: ${SMS_ENABLED:false}
    provider: ${SMS_PROVIDER:aliyun}
    
  # 地图服务配置
  map:
    provider: ${MAP_PROVIDER:amap}
    api-key: ${MAP_API_KEY:your-map-api-key}
    
  # 业务配置
  business:
    # 用户配置
    user:
      max-footprints-per-user: 1000
      max-paths-per-footprint: 50
      registration-enabled: true
      
    # 足迹配置
    footprint:
      max-title-length: 100
      max-description-length: 1000
      max-images-per-footprint: 10
      
    # 路径配置
    path:
      max-points-per-path: 1000
      max-distance-km: 1000
      
  # 安全配置
  security:
    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special-char: false
      
    # 登录策略
    login:
      max-attempts: 5
      lockout-duration: 300000 # 5分钟
      
    # 会话配置
    session:
      timeout: 1800000 # 30分钟
      max-concurrent: 3

# 日志配置
logging:
  level:
    com.trek0.api: INFO
    org.springframework.web: INFO
    io.github.resilience4j: INFO