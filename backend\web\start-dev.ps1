# Trek0 开发环境启动脚本
# 请根据您的PostgreSQL配置修改以下变量

Write-Host "=== Trek0 开发环境启动脚本 ===" -ForegroundColor Green

# 设置数据库环境变量
$env:DB_USERNAME = "postgres"
$env:DB_PASSWORD = "080706"  # 请修改为您的PostgreSQL密码

# 设置JWT密钥
$env:JWT_SECRET = "trek0-jwt-secret-key-for-development"

# 设置文件上传目录
$env:UPLOAD_DIR = "./uploads"

Write-Host "环境变量已设置:" -ForegroundColor Yellow
Write-Host "DB_USERNAME: $env:DB_USERNAME" -ForegroundColor Cyan
Write-Host "DB_PASSWORD: $env:DB_PASSWORD" -ForegroundColor Cyan
Write-Host "JWT_SECRET: $env:JWT_SECRET" -ForegroundColor Cyan
Write-Host "UPLOAD_DIR: $env:UPLOAD_DIR" -ForegroundColor Cyan

Write-Host "`n正在启动应用..." -ForegroundColor Green
Write-Host "请确保PostgreSQL服务正在运行，并且已创建 trek0_dev 数据库" -ForegroundColor Yellow

# 启动应用
mvn spring-boot:run 