package com.trek0.service.impl;

import com.trek0.domain.model.UserLoginRecord;
import com.trek0.domain.repository.UserLoginRecordRepository;
import com.trek0.service.UserLoginRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserLoginRecordServiceImpl implements UserLoginRecordService {

    private static final Logger logger = LoggerFactory.getLogger(UserLoginRecordServiceImpl.class);

    private final UserLoginRecordRepository loginRecordRepository;

    @Autowired
    public UserLoginRecordServiceImpl(UserLoginRecordRepository loginRecordRepository) {
        this.loginRecordRepository = loginRecordRepository;
    }

    /**
     * 异步记录用户登录信息
     * 使用异步处理确保登录记录功能不影响正常登录流程
     */
    @Async
    @Transactional
    public void recordLoginAsync(Long userId, String ipAddress, String userAgent) {
        try {
            // 获取IP地址位置信息
            String ipLocation = "未知";
            try {
                // ipLocation = ipLocationUtil.getRegion(ipAddress); // Removed as per edit hint
            } catch (Exception e) {
                logger.warn("获取IP位置信息失败: {}", e.getMessage());
            }
            
            // 解析设备信息
            String deviceInfo = "未知"; // deviceInfoUtil.getDeviceInfo(userAgent); // Removed as per edit hint
            String browser = "未知"; // deviceInfoUtil.getBrowserInfo(userAgent); // Removed as per edit hint
            String operatingSystem = "未知"; // deviceInfoUtil.getOperatingSystem(userAgent); // Removed as per edit hint
            
            // 创建登录记录
            UserLoginRecord loginRecord = new UserLoginRecord();
            loginRecord.setUserId(userId);
            loginRecord.setIpAddress(ipAddress);
            loginRecord.setIpLocation(ipLocation);
            loginRecord.setDeviceInfo(deviceInfo);
            loginRecord.setUserAgent(userAgent);
            loginRecord.setBrowser(browser);
            loginRecord.setOperatingSystem(operatingSystem);
            loginRecord.setLoginTime(LocalDateTime.now());
            
            // 保存到数据库
            loginRecordRepository.save(loginRecord);
            
            logger.info("用户登录记录保存成功 - 用户ID: {}, IP: {}, 位置: {}, 设备: {}", 
                       userId, ipAddress, ipLocation, deviceInfo);
            
        } catch (Exception e) {
            // 记录错误但不抛出异常，确保不影响用户正常登录
            logger.error("记录用户登录信息失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 获取用户的登录记录（分页）
     */
    public Page<UserLoginRecord> getUserLoginRecords(Long userId, Pageable pageable) {
        return loginRecordRepository.findByUserIdOrderByLoginTimeDesc(userId, pageable);
    }

    /**
     * 获取用户最近的登录记录
     */
    public List<UserLoginRecord> getRecentLoginRecords(Long userId) {
        return loginRecordRepository.findTop10ByUserIdOrderByLoginTimeDesc(userId);
    }

    /**
     * 获取用户总登录次数
     */
    public long getUserLoginCount(Long userId) {
        return loginRecordRepository.countByUserId(userId);
    }

    /**
     * 根据用户ID获取最后一次登录记录
     */
    public UserLoginRecord getLastLoginRecord(Long userId) {
        return loginRecordRepository.findTop10ByUserIdOrderByLoginTimeDesc(userId).stream().findFirst().orElse(null);
    }

    /**
     * 根据用户ID和时间范围获取登录记录
     */
    public List<UserLoginRecord> getUserLoginRecordsByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return loginRecordRepository.findByUserIdAndLoginTimeBetweenOrderByLoginTimeDesc(userId, startTime, endTime, Pageable.unpaged()).getContent();
    }

    /**
     * 获取用户登录热力图数据
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份 (1-12)
     * @return 热力图数据和统计信息
     */
    public Map<String, Object> getUserLoginHeatmapData(Long userId, int year, int month) {
        // 计算月份的开始和结束时间
        LocalDateTime startOfMonth = LocalDateTime.of(year, month, 1, 0, 0, 0);
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusSeconds(1);
        
        // 获取该月份的所有登录记录
        List<UserLoginRecord> records = loginRecordRepository.findByUserIdAndLoginTimeBetweenOrderByLoginTimeDesc(userId, startOfMonth, endOfMonth, Pageable.unpaged()).getContent();
        
        // 按日期统计登录次数
        Map<Integer, Integer> dailyLoginCounts = new HashMap<>();
        for (UserLoginRecord record : records) {
            int dayOfMonth = record.getLoginTime().getDayOfMonth();
            dailyLoginCounts.put(dayOfMonth, dailyLoginCounts.getOrDefault(dayOfMonth, 0) + 1);
        }
        
        // 计算统计数据
        int totalLoginCount = records.size();
        int activeDays = dailyLoginCounts.size();
        int maxDailyLogins = dailyLoginCounts.values().stream().mapToInt(Integer::intValue).max().orElse(0);
        int maxLoginDay = dailyLoginCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(1);
        
        // 计算连续登录天数
        int consecutiveLoginDays = calculateConsecutiveLoginDays(userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("dailyLoginCounts", dailyLoginCounts);
        result.put("totalLoginCount", totalLoginCount);
        result.put("activeDays", activeDays);
        result.put("consecutiveLoginDays", consecutiveLoginDays);
        result.put("maxDailyLogins", maxDailyLogins);
        result.put("maxLoginDay", maxLoginDay);
        
        return result;
    }
    
    /**
     * 计算用户连续登录天数
     * @param userId 用户ID
     * @return 连续登录天数
     */
    private int calculateConsecutiveLoginDays(Long userId) {
        // 获取最近30天的登录记录
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        List<UserLoginRecord> recentRecords = loginRecordRepository.findByUserIdAndLoginTimeBetweenOrderByLoginTimeDesc(userId, thirtyDaysAgo, LocalDateTime.now(), Pageable.unpaged()).getContent();
        
        if (recentRecords.isEmpty()) {
            return 0;
        }
        
        // 按日期分组
        Set<LocalDate> loginDates = recentRecords.stream()
            .map(record -> record.getLoginTime().toLocalDate())
            .collect(Collectors.toSet());
        
        // 从今天开始往前计算连续天数
        LocalDate today = LocalDate.now();
        int consecutiveDays = 0;
        
        for (int i = 0; i < 30; i++) {
            LocalDate checkDate = today.minusDays(i);
            if (loginDates.contains(checkDate)) {
                consecutiveDays++;
            } else {
                break;
            }
        }
        
        return consecutiveDays;
    }
}