# TREK0 Backend - 多模块企业级架构

## 项目概述
TREK0是一个基于Spring Boot的多模块企业级后端项目，采用DDD（领域驱动设计）架构，严格遵循分层依赖原则。

## 技术栈
- **Java**: JDK 24
- **Spring Boot**: 3.2.3
- **Spring Cloud**: 2023.0.0
- **数据库**: PostgreSQL
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **监控**: Micrometer + Prometheus
- **限流**: Bucket4j
- **熔断**: Resilience4j
- **文档**: OpenAPI 3.0

## 模块架构

### 核心模块
```
trek0-parent/          # 父工程 - 统一依赖管理
├── trek0-api/         # API层 - DTO、接口定义
├── trek0-domain/      # 领域层 - 领域模型、Repository接口
├── trek0-service/     # 服务层 - 业务逻辑
├── trek0-infra/       # 基础设施层 - 数据访问实现
├── trek0-web/         # Web层 - REST API控制器
├── trek0-common/      # 通用层 - 工具类、常量
└── trek0-config/      # 配置层 - Spring配置、Bean定义
```

### 辅助模块
```
├── trek0-scripts/     # 脚本模块 - 数据库迁移、运维脚本
├── trek0-docs/        # 文档模块 - 设计文档、API文档
└── trek0-test/        # 测试模块 - 集成测试、端到端测试
```

## 依赖关系
```
web → service → domain → api
service → infra → domain
web, service, infra, domain, api → common, config
```

## 快速开始

### 环境要求
- JDK 24
- Maven 3.9+
- PostgreSQL 14+
- Redis 6+

### 编译运行
```bash
# 编译所有模块
mvn clean install

# 运行应用
mvn spring-boot:run -pl web

# 运行测试
mvn test

# 数据库迁移
mvn flyway:migrate
```

### 开发模式
```bash
# 开发环境启动
mvn spring-boot:run -pl web -Dspring.profiles.active=dev

# 热重载（需要IDE支持）
# 修改代码后自动重启
```

## 开发规范

### 代码风格
- 使用Checkstyle进行代码风格检查
- 使用Spotless进行代码格式化
- 遵循阿里巴巴Java开发手册

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心业务流程
- 使用TestContainers进行集成测试

### 提交规范
- 使用Git Flow分支管理
- 提交信息遵循Conventional Commits规范
- 代码审查必须通过才能合并

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t trek0-backend .

# 运行容器
docker run -p 8080:8080 trek0-backend
```

### Kubernetes部署
```bash
# 应用配置
kubectl apply -f k8s/

# 查看状态
kubectl get pods -l app=trek0-backend
```

## 监控运维

### 健康检查
- 端点：`/actuator/health`
- 数据库连接检查
- Redis连接检查

### 监控指标
- 端点：`/actuator/metrics`
- 业务指标：用户注册、足迹创建等
- 系统指标：CPU、内存、响应时间等

### 日志管理
- 使用Logback进行日志配置
- 结构化日志输出
- 日志级别：ERROR > WARN > INFO > DEBUG

## 文档
- [API文档](./docs/api/)
- [架构设计](./docs/architecture/)
- [部署指南](./docs/deployment/)
- [开发指南](./docs/development/)

## 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request
5. 等待代码审查

## 许可证
MIT License 