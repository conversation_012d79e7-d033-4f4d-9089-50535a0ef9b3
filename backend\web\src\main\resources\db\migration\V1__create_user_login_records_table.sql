-- 创建用户登录记录表
CREATE TABLE IF NOT EXISTS user_login_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    ip_location VARCHAR(255),
    device_info VARCHAR(500),
    user_agent TEXT,
    browser VARCHAR(100),
    operating_system VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_login_records_user_id ON user_login_records(user_id);
CREATE INDEX IF NOT EXISTS idx_user_login_records_login_time ON user_login_records(login_time);
CREATE INDEX IF NOT EXISTS idx_user_login_records_user_id_login_time ON user_login_records(user_id, login_time DESC);
CREATE INDEX IF NOT EXISTS idx_user_login_records_ip_address ON user_login_records(ip_address);

-- 添加外键约束（如果需要的话，可选）
-- ALTER TABLE user_login_records ADD CONSTRAINT fk_user_login_records_user_id 
--     FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 添加注释
COMMENT ON TABLE user_login_records IS '用户登录记录表';
COMMENT ON COLUMN user_login_records.id IS '主键ID';
COMMENT ON COLUMN user_login_records.user_id IS '用户ID';
COMMENT ON COLUMN user_login_records.login_time IS '登录时间';
COMMENT ON COLUMN user_login_records.ip_address IS 'IP地址';
COMMENT ON COLUMN user_login_records.ip_location IS 'IP地址位置信息';
COMMENT ON COLUMN user_login_records.device_info IS '设备信息';
COMMENT ON COLUMN user_login_records.user_agent IS '用户代理字符串';
COMMENT ON COLUMN user_login_records.browser IS '浏览器信息';
COMMENT ON COLUMN user_login_records.operating_system IS '操作系统信息';