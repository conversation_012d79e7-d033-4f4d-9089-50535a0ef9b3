-- 企业标准足迹照片表 - 重构版本

CREATE TABLE footprint_photos (
    photo_id BIGSERIAL PRIMARY KEY,
    footprint_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_filename VARCHAR(255),
    image_size_bytes BIGINT,
    image_width INTEGER,
    image_height INTEGER,
    image_format VARCHAR(10),
    upload_method VARCHAR(30) NOT NULL DEFAULT 'WEB',
    photo_description TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    is_cover_photo BOOLEAN NOT NULL DEFAULT FALSE,
    photo_status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    uploaded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_photo_footprint FOREIGN KEY (footprint_id) REFERENCES footprints(footprint_id) ON DELETE CASCADE,
    
    -- 业务约束
    CONSTRAINT chk_image_size CHECK (image_size_bytes IS NULL OR image_size_bytes > 0),
    CONSTRAINT chk_image_dimensions CHECK (
        (image_width IS NULL AND image_height IS NULL) OR 
        (image_width > 0 AND image_height > 0)
    ),
    CONSTRAINT chk_upload_method CHECK (upload_method IN ('WEB', 'MOBILE', 'API', 'BATCH')),
    CONSTRAINT chk_photo_status CHECK (photo_status IN ('ACTIVE', 'PROCESSING', 'FAILED', 'DELETED')),
    CONSTRAINT chk_image_format CHECK (image_format IN ('JPG', 'JPEG', 'PNG', 'WEBP', 'GIF'))
);

-- 企业级索引
CREATE INDEX idx_footprint_photos_footprint_id ON footprint_photos(footprint_id, display_order);
CREATE INDEX idx_footprint_photos_cover ON footprint_photos(footprint_id, is_cover_photo);
CREATE INDEX idx_footprint_photos_status ON footprint_photos(photo_status);
CREATE INDEX idx_footprint_photos_uploaded_at ON footprint_photos(uploaded_at DESC);

-- 企业级注释
COMMENT ON TABLE footprint_photos IS '企业标准足迹照片表';
COMMENT ON COLUMN footprint_photos.photo_id IS '照片主键ID';
COMMENT ON COLUMN footprint_photos.footprint_id IS '足迹ID（外键）';
COMMENT ON COLUMN footprint_photos.image_url IS '图片URL地址';
COMMENT ON COLUMN footprint_photos.image_filename IS '图片文件名';
COMMENT ON COLUMN footprint_photos.image_size_bytes IS '图片大小（字节）';
COMMENT ON COLUMN footprint_photos.image_width IS '图片宽度（像素）';
COMMENT ON COLUMN footprint_photos.image_height IS '图片高度（像素）';
COMMENT ON COLUMN footprint_photos.image_format IS '图片格式';
COMMENT ON COLUMN footprint_photos.upload_method IS '上传方式';
COMMENT ON COLUMN footprint_photos.photo_description IS '照片描述';
COMMENT ON COLUMN footprint_photos.display_order IS '显示顺序';
COMMENT ON COLUMN footprint_photos.is_cover_photo IS '是否为封面照片';
COMMENT ON COLUMN footprint_photos.photo_status IS '照片状态';
COMMENT ON COLUMN footprint_photos.uploaded_at IS '上传时间';
COMMENT ON COLUMN footprint_photos.processed_at IS '处理完成时间';