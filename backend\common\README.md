# Common Module

## 职责说明
Common模块是系统的通用工具层，包含：
- 工具类
- 常量定义
- 通用异常
- 基础抽象类
- 全局枚举
- 通用注解

## 模块依赖
- 无外部依赖（基础模块）
- 可被其他所有模块依赖

## 包结构
```
com.trek0.common/
├── util/           # 工具类
│   ├── IpLocationUtil.java
│   ├── DeviceInfoUtil.java
│   └── ...
├── constant/       # 常量定义
├── exception/      # 通用异常
├── enums/          # 全局枚举
├── annotation/     # 通用注解
└── metrics/        # 监控指标
    └── BusinessMetrics.java
```

## 核心工具类
- **IpLocationUtil**: IP地址定位工具
- **DeviceInfoUtil**: 设备信息工具
- **BusinessMetrics**: 业务监控指标
- **T0IdGenerator**: ID生成器

## 开发规范
- 工具类应该是无状态的
- 常量使用final修饰
- 异常类继承自RuntimeException
- 枚举类使用大写命名
- 避免在common模块中包含业务逻辑 