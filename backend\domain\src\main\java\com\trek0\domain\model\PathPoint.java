package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "path_points", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"path_id", "point_sequence"}))
public class PathPoint {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "point_id")
    private Long pointId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "path_id", nullable = false)
    @JsonBackReference
    private FootprintPath path;
    
    @Column(name = "latitude", nullable = false, precision = 10, scale = 8)
    @NotNull(message = "Latitude is required")
    @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
    private BigDecimal latitude;
    
    @Column(name = "longitude", nullable = false, precision = 11, scale = 8)
    @NotNull(message = "Longitude is required")
    @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
    private BigDecimal longitude;
    
    @Column(name = "elevation_meters", precision = 8, scale = 2)
    private BigDecimal elevationMeters;
    
    @Column(name = "point_sequence", nullable = false)
    @NotNull(message = "Point sequence is required")
    @Min(value = 0, message = "Point sequence must be non-negative")
    private Integer pointSequence;
    
    @Column(name = "recorded_at", nullable = false)
    @NotNull(message = "Recorded time is required")
    private LocalDateTime recordedAt;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "point_type", length = 20)
    private PointType pointType = PointType.TRACK;
    
    @Column(name = "speed_kmh", precision = 6, scale = 2)
    private BigDecimal speedKmh;
    
    @Column(name = "bearing_degrees", precision = 6, scale = 2)
    private BigDecimal bearingDegrees;
    
    @Column(name = "accuracy_meters", precision = 6, scale = 2)
    private BigDecimal accuracyMeters;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    public enum PointType {
        START, END, WAYPOINT, TRACK
    }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        if (recordedAt == null) {
            recordedAt = LocalDateTime.now();
        }
    }
    
    // Constructors
    public PathPoint() {}
    
    public PathPoint(BigDecimal latitude, BigDecimal longitude, Integer pointSequence) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.pointSequence = pointSequence;
        this.recordedAt = LocalDateTime.now();
    }
    
    public PathPoint(BigDecimal latitude, BigDecimal longitude, Integer pointSequence, LocalDateTime recordedAt) {
        this.latitude = latitude;
        this.longitude = longitude;
        this.pointSequence = pointSequence;
        this.recordedAt = recordedAt;
    }
    
    // Getters and Setters
    public Long getPointId() {
        return pointId;
    }
    
    public void setPointId(Long pointId) {
        this.pointId = pointId;
    }
    
    public FootprintPath getPath() {
        return path;
    }
    
    public void setPath(FootprintPath path) {
        this.path = path;
    }
    
    public BigDecimal getLatitude() {
        return latitude;
    }
    
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }
    
    public BigDecimal getLongitude() {
        return longitude;
    }
    
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }
    
    public BigDecimal getElevationMeters() {
        return elevationMeters;
    }
    
    public void setElevationMeters(BigDecimal elevationMeters) {
        this.elevationMeters = elevationMeters;
    }
    
    public Integer getPointSequence() {
        return pointSequence;
    }
    
    public void setPointSequence(Integer pointSequence) {
        this.pointSequence = pointSequence;
    }
    
    public LocalDateTime getRecordedAt() {
        return recordedAt;
    }
    
    public void setRecordedAt(LocalDateTime recordedAt) {
        this.recordedAt = recordedAt;
    }
    
    public PointType getPointType() {
        return pointType;
    }
    
    public void setPointType(PointType pointType) {
        this.pointType = pointType;
    }
    
    public BigDecimal getSpeedKmh() {
        return speedKmh;
    }
    
    public void setSpeedKmh(BigDecimal speedKmh) {
        this.speedKmh = speedKmh;
    }
    
    public BigDecimal getBearingDegrees() {
        return bearingDegrees;
    }
    
    public void setBearingDegrees(BigDecimal bearingDegrees) {
        this.bearingDegrees = bearingDegrees;
    }
    
    public BigDecimal getAccuracyMeters() {
        return accuracyMeters;
    }
    
    public void setAccuracyMeters(BigDecimal accuracyMeters) {
        this.accuracyMeters = accuracyMeters;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    // 向后兼容性方法
    public Long getId() {
        return pointId;
    }
    
    public void setId(Long id) {
        this.pointId = id;
    }
    
    // 业务方法
    public boolean isStartPoint() {
        return pointType == PointType.START;
    }
    
    public boolean isEndPoint() {
        return pointType == PointType.END;
    }
    
    public boolean isWaypoint() {
        return pointType == PointType.WAYPOINT;
    }
    
    public boolean isTrackPoint() {
        return pointType == PointType.TRACK;
    }
    
    /**
     * 计算与另一个点的距离（米）
     */
    public double distanceTo(PathPoint other) {
        if (other == null) {
            return 0.0;
        }
        
        double lat1 = this.latitude.doubleValue();
        double lon1 = this.longitude.doubleValue();
        double lat2 = other.latitude.doubleValue();
        double lon2 = other.longitude.doubleValue();
        
        return calculateDistance(lat1, lon1, lat2, lon2);
    }
    
    /**
     * 使用Haversine公式计算两点间距离
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371000; // 地球半径（米）
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * 检查坐标是否有效
     */
    public boolean hasValidCoordinates() {
        return latitude != null && longitude != null
                && latitude.compareTo(new BigDecimal("-90")) >= 0
                && latitude.compareTo(new BigDecimal("90")) <= 0
                && longitude.compareTo(new BigDecimal("-180")) >= 0
                && longitude.compareTo(new BigDecimal("180")) <= 0;
    }
    
    // 兼容service层
    public void setLatitudeDouble(Double lat) { this.latitude = lat != null ? new java.math.BigDecimal(lat) : null; }
    public void setLongitudeDouble(Double lng) { this.longitude = lng != null ? new java.math.BigDecimal(lng) : null; }
    public void setElevation(Double elevation) { this.elevationMeters = elevation != null ? new java.math.BigDecimal(elevation) : null; }
    public void setTimestamp(java.time.LocalDateTime timestamp) { this.recordedAt = timestamp; }
    public void setSequence(int sequence) { this.pointSequence = sequence; }
    
    @Override
    public String toString() {
        return String.format("PathPoint{pointId=%d, lat=%s, lon=%s, sequence=%d, type=%s}", 
                pointId, latitude, longitude, pointSequence, pointType);
    }
}